<!DOCTYPE html>
<html lang="fr-FR">
    <head>
        <title>CSGOSHOP</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <!-- inclus bootstrap via CDN = Content Delivery Network (réseau de distribution de contenu) -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
        <!-- inclus style.css -->
        <link href="<?php ROOT?>/view/CSS/style.css" rel="stylesheet">
        <link href="<?php ROOT?>/view/CSS/style6bis.css" rel="stylesheet">
        <link href="<?php ROOT?>/view/CSS/panier.css" rel="stylesheet">

    </head>
    <body class="custom-body">
        <div class="page-wrapper">
            <!-- Contenu principal -->
            <div class="container mt-5">
                <!-- Progress Steps -->
                <div class="progress-steps mb-5">
                    <div class="step active">
                        <div class="step-icon">
                            <i class="bi bi-cart-fill"></i>
                            <span class="step-number">1</span>
                        </div>
                        <span class="step-label">Panier</span>
                    </div>
                    <div class="step active">
                        <div class="step-icon">
                            <i class="bi bi-truck"></i>
                            <span class="step-number">2</span>
                        </div>
                        <span class="step-label">Livraison</span>
                    </div>
                    <div class="step">
                        <div class="step-icon">
                            <i class="bi bi-credit-card"></i>
                            <span class="step-number">3</span>
                        </div>
                        <span class="step-label">Paiement</span>
                    </div>
                </div>

                <!-- Formulaire de livraison -->
                <form id="form_livraison" action="/Livraison/" method="POST">
                    <div class="row justify-content-center">
                        <div class="col-md-6">
                            <h2 class="text-center">Informations de livraison</h2>
                            <div class="mb-3">
                                <label for="nom" class="form-label">Nom</label>
                                <input type="text" class="form-control" id="nom" name="nom" value="<?php echo $params['nom']; ?>" required>
                            </div>
                            <div class="mb-3">
                                <label for="prenom" class="form-label">Prénom</label>
                                <input type="text" class="form-control" id="prenom" name="prenom" value="<?php echo $params['prenom']; ?>" required>
                            </div>
                            <div class="mb-3">
                                <label for="adresse" class="form-label">Adresse</label>
                                <input type="text" class="form-control" id="adresse" name="adresse" value="<?php echo $params['adresse']; ?>" required>
                            </div>
                            <div class="mb-3">
                                <label for="cp" class="form-label">Code postal</label>
                                <input type="text" class="form-control" id="cp" name="cp" value="<?php echo $params['cp']; ?>" required>
                            </div>
                            <div class="mb-3">
                                <label for="ville" class="form-label">Ville</label>
                                <input type="text" class="form-control" id="ville" name="ville" value="<?php echo $params['ville']; ?>" required>
                            </div>
                            <div class="mb-3">
                                <label for="pays" class="form-label">Pays</label>
                                <input type="text" class="form-control" id="pays" name="pays" value="<?php echo $params['pays']; ?>" required>
                            </div>
                        </div>
                    </div>
                </form>



                <!-- Boutons de navigation -->
                <div class="d-flex justify-content-between mt-4 mb-5">
                    <a href="/Panier/" class="btn btn-outline-dark">
                        <i class="bi bi-arrow-left"></i> Retour
                    </a>
                    <button type="button" id="button_paiement" class="btn btn-primary">
                        Continuer vers le paiement <i class="bi bi-arrow-right"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <?php include_once ROOT.'/view/template/footer.php'; ?>


        <!-- Chargement javascript -->
        <!-- js pour bootstrap -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script>
        <script src="<?php ROOT?>/Js/formLivraison.js"></script>

    </body>
</html>
