# Activer le moteur de réécriture
Options +FollowSymlinks
RewriteEngine on
RewriteBase /


# Permissions de base
<IfModule mod_authz_core.c>
    Require all granted
</IfModule>


# Redirections des URL "neat" vers les scripts PHP avec les bons paramètres
RewriteRule ^/$ index.php?controller=Accueil&action=index [NC,L]
RewriteRule ^VenteFlash/$ index.php?controller=VenteFlash&action=index [NC,L]
RewriteRule ^MeilleursVentes/$ index.php?controller=MeilleursVentes&action=index [NC,L]
RewriteRule ^Connexion/$ index.php?controller=Connexion&action=index [NC,L]
RewriteRule ^MonProfil/$ index.php?controller=MonProfil&action=index [NC,L]
RewriteRule ^Inscription/$ index.php?controller=Inscription&action=index [NC,L]
RewriteRule ^ValidationInscription/$ index.php?controller=ValidationInscription&action=index [NC,L]
RewriteRule ^MonProfil/PageAdresse/$ index.php?controller=PageAdresse&action=index [NC,L]
RewriteRule ^MonProfil/PageSecurite/$ index.php?controller=PageSecurite&action=index [NC,L]
RewriteRule ^Panier/$ index.php?controller=PagePanier&action=index [NC,QSA,L]
RewriteRule ^Livraison/$ index.php?controller=Livraison&action=index [NC,L]
RewriteRule ^Paiement/$ index.php?controller=Paiement&action=index [NC,L]
RewriteRule ^ConfirmationCommande/$ index.php?controller=ConfirmationCommande&action=index [NC,L]
RewriteRule ^MonProfil/Commandes/$ index.php?controller=PageCommandes&action=index [NC,L]
RewriteRule ^PageProduits/([^/]+)/ajoutpanier/?$ index.php?controller=PageProduits&action=ajoutpanier&Produit=$1 [NC,QSA,L]
RewriteRule ^Recherche/([^/]+)/?$ index.php?controller=Recherche&action=index&Mots=$1 [NC,QSA,L]


RewriteRule ^$ index.php?controller=Accueil&action=selectmulti [QSA,L]
RewriteRule ^VenteFlash/Filtres$ index.php?controller=VenteFlash&action=selectmulti [QSA,L]
RewriteRule ^MeilleursVentes/Filtres$ index.php?controller=MeilleursVentes&action=selectmulti [QSA,L]
RewriteRule ^Recherche/recherche$ index.php?controller=Recherche&action=recherche [NC,QSA,L]

RewriteRule ^PageProduits/([^/]+)/?$ index.php?controller=PageProduits&action=index&Produit=$1 [NC,QSA,L]

RewriteRule ^BO-Admin/$ index.php?controller=ConnexionBackOffice&action=index [NC,L]
RewriteRule ^BO-Admin/Accueil/$ index.php?controller=AccueilBackOffice&action=index [NC,L]
RewriteRule ^BO-Admin/Accueil/gestioncompte/$ index.php?controller=GestionCompteBackOffice&action=index [NC,L]
RewriteRule ^BO-Admin/Accueil/gestionproduits/$ index.php?controller=GestionProduitsBackOffice&action=index [NC,L]
RewriteRule ^BO-Admin/Accueil/gestioncommandes/$ index.php?controller=GestionCommandesBackOffice&action=index [NC,L]
RewriteRule ^BO-Admin/Accueil/pagesecurite/$ index.php?controller=PageSecuriteBackOffice&action=index [NC,L]
RewriteRule ^BO-Admin/PageSecurite/$ index.php?controller=PageSecuriteBackOffice&action=index [NC,L]

# Gérer les actions POST
RewriteRule ^BO-Admin/Accueil/gestioncompte/ajaxPrivileges/$ index.php?controller=GestionCompteBackOffice&action=ajaxPrivileges [NC,L]

# Gérer les requêtes DELETE
