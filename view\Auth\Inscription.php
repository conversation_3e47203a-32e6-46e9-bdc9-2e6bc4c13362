<!DOCTYPE html>
<html lang="fr-FR">
    <head>
        <title>CSGOSHOP</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <!-- inclus bootstrap via CDN = Content Delivery Network (réseau de distribution de contenu) -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <!-- inclus style.css -->
        <link href="<?php ROOT?>/view/CSS/style2.css" rel="stylesheet">

    </head>
    <body>    
        <div class="container">
            <div class="row">
                <!-- Bouton de retour -->
                <div class="col col-lg-3 mt-5">
                    <a href="/" class="text-dark underline-change"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-left" viewBox="0 0 16 18">
                    <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8"/>
                    </svg> Retour</a>
                </div>

                <!-- Logo CSGOSHOP -->
                <div class="col text-center">
                    <img src="<?php ROOT?>/public/Images/CSGOSHOP LOGO.png" width="100" alt="">
                </div>

                <div class="col col-lg-3"></div>
            </div>
            
        </div>
        
        <!-- Formulaire d'inscription -->
        <div class="container position-absolute top-50 start-50 translate-middle">
            <form action="/Inscription/" method="POST">
                <div class="row">
                        <div class="col col-lg-3">
                        </div>
                    
                        <div class="col shadow-sm border rounded-3 bg-light">
                            <h1 class="text-center mt-3">Inscription</h1>
                            <div class="row text-center ps-3 mt-3">
                                <!-- Prénom & Nom de l'utilisateur -->
                                <div class="col-5 text-center ps-5 pe-4">
                                    <?php echo $params['htmlp']; ?> <!-- Afficher le message d'erreur ici -->
                                    <input type="text" class="form-control" id="prenom" name="prenom" placeholder="Prénom">
                                </div>

                                <div class="col-5 text-center ps-5 pe-4">
                                    <?php echo $params['htmln']; ?> <!-- Afficher le message d'erreur ici -->
                                    <input type="text" class="form-control" id="nom" name="nom" placeholder="Nom">
                                </div>
                            </div>

                            <!-- Identifiant de l'utilisateur -->
                            <div class="row ps-3 mt-2">
                                <div class="col-7 text-center ps-5 pe-4">
                                    <?php echo $params['html']; ?> <!-- Afficher le message d'erreur ici -->
                                    <input type="text" class="form-control" id="user" name="id" placeholder="Nom d'utilisateur">
                                </div>
                            </div>
                            <!-- Mot de passe de l'utilisateur -->
                            <div class="row ps-3 mt-4">
                                <div class="col-10 text-center mt-2 ps-5">
                                    <?php echo $params['htmlpass']; ?> <!-- Afficher le message d'erreur ici -->
                                    <input type="password" class="form-control" id="password" name="password" placeholder="Mot de passe">
                                </div>

                                <div class=" col-10 text-center mt-2 ps-5">
                                    <?php echo $params['htmlpassconfirm']; ?> <!-- Afficher le message d'erreur ici -->
                                    <input type="password" class="form-control" id="confirmpassword" name="confirmpassword" placeholder="Confirmer le Mot de passe">
                                </div>
                            </div>
                            <!-- Information de l'utilisateur -->
                            <div class="row ps-3 mt-4">
                                <div class="col-11 text-center mt-2 ps-5">
                                    <?php echo $params['htmlemail']; ?> <!-- Afficher le message d'erreur ici -->
                                    <input type="email" class="form-control" id="email" name="email" placeholder="Adresse mail">
                                </div>

                                <div class="col-11 text-center mt-2 ps-5">
                                    <?php echo $params['htmltel']; ?> <!-- Afficher le message d'erreur ici -->
                                    <input type="tel" class="form-control" id="tel" name="tel" placeholder="N° Telephone (facultatif)">
                                </div>
                            </div>


                            <div class="text-center mt-5 mb-3">
                                <input class="btn btn-outline-dark" type="submit" id="connexion" value="S'inscrire">
                            </div>
                        </div>

                        <div class="col col-lg-3">
                        </div>
                </div>
                <!-- Lien de connexion -->
                <div class="row text-center mt-3">
                    <a href="/Connexion/" class="text-dark underline-change2">Connectez-vous !</a>
                </div>
            </form>
        </div>

    </body>
</html>