<?php
class PageProduitsController extends Controller{


    public static function index($params) {
        // appelle la vue
        $view = ROOT."/view/pageproduits.php";
        self::render($view, $params);
    }

    /**
     * Action qui ajoute un produit dans le panier
     * params : tableau des paramètres
     */
    public static function ajoutpanier($params) {
        // Initialisation de DBManagerProduits et DBManagerPanier
        $dbManager = new DBManagerProduits();
        $dbManagerPanier = new DBManagerPanier();
        
        //Vérifie si l'utilisateur est connecté et si il a appuyé sur le bouton pour ajouter un produit dans le panier
        if(isset($_GET['ProduitPanier']) && !empty($_SESSION['user'])){
            $produitNom = $_GET['ProduitPanier'];
            $quantite = $_GET['Quantite'];
            
            // Récupère l'ID du produit 
            $idProduitSelectionne = DBManagerProduits::getProduitSelect($produitNom)->getID();
            // Récupère l'ID du client
            $idClient = DBManagerClient::getClientByName2($_SESSION['user'])->getID();
                
            // Vérifie si le produit est déjà dans le panier
            $panierExistant = DBManagerPanier::getPanierByUser($idClient);
            $produitExiste = false;
                
            // Met à jour la quantité si le produit existe déjà
            foreach($panierExistant as $item) {
                if($item->getIdProduit() == $idProduitSelectionne) {
                    $nouvelleQuantite = $item->getQte() + $quantite;
                    DBManagerPanier::updateQuantitePanier($idClient, $idProduitSelectionne, $nouvelleQuantite);
                    $produitExiste = true;
                    break;
                }
            }
                
            if(!$produitExiste) {
                // Ajoute le produit dans le panier
                DBManagerPanier::AjouterProduitPanier($idClient, $idProduitSelectionne, $quantite);
            }

            // Redirige vers la page des produits
            header("Location: /PageProduits/". urlencode($produitNom) ."/");

            // Message de succès (Obligé de faire une variable $_SESSION car $params n'est pas accessible dans la vue étant donné qu'on revient sur la page du produit)
            $_SESSION['success_message'] = '<p style="color:green">Produit ajouté au panier avec succès !</p>';
            

        }
        //Redirige vers la page de connexion si l'utilisateur n'est pas connecté
        else if(isset($_GET['ProduitPanier']) && empty($_SESSION['user'])){
            header("Location: /Connexion/");
            exit();
        }
        
        // appelle la vue
        $view = ROOT."/view/pageproduits.php";
        self::render($view, $params);
    }

}
?>
