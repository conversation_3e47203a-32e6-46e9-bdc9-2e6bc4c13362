<?php
class GestionProduitsBackOfficeController extends Controller {
    public static function index($params) {
        
        // Vérifie si l'utilisateur est connecté en tant qu'admin
        if (!isset($_SESSION['User'])) {
            // Redirige vers la page de connexion si non connecté
            header("Location: /BO-Admin/");
            exit();
        }


        // Vérifie si l'admin a appuyer sur le bouton pour réinitialiser les produits
        if (isset($_POST['ResetProduits'])) {
            DBManagerProduits::FixturesProduits();
        }


        // Vérifie si l'admin a appuyer sur le bouton pour supprimer un produit
        if(isset($_POST['Delete'])){
            DBManagerProduits::DelProduitsByName($_POST['Delete']);
        }
        


        // Parcours toutes la liste des produits et le met dans un tableau
        $Tabproduits = [];
        foreach(DBManagerProduits::getLesProduits() as $produit){
            $Tabproduits[] = $produit;
        }
        // Parcours tous les produits du tableau et le met dans bouton défilant
        $params['htmloptionselect'] = '';
        for($i=0; $i<count($Tabproduits); $i++){
            $params['htmloptionselect'] .= '<option value="'.$Tabproduits[$i]->getNom().'">'.$Tabproduits[$i]->getNom().'</option>';
        }
        // Modifie le stock si l'admin a appuyer sur le bouton et qu'il ait saisie le nombre de stock et qu'il a selectionné un produit
        if(!empty($_POST['NombreStock']) && isset($_POST['Selectproduits']) && isset($_POST['Bouton'])){
            DBManagerProduits::ModifStockProduit(htmlspecialchars($_POST['NombreStock']) ,$_POST['Selectproduits']);
        }



        // Parcours toutes la liste des catégories de produit et le met dans un tableau
        $Tabcateg = [];
        foreach(DBManagerCategorie::getLesCategories() as $categ){
            $Tabcateg[] = $categ;
        }
        
        // Parcours tous les catégories du tableau et le met dans bouton défilant
        $params['htmloptionselectcateg'] = '';
        for($i=0; $i<count($Tabcateg); $i++){
            $params['htmloptionselectcateg'] .= '<option value="'.$Tabcateg[$i]->getID().'">'.$Tabcateg[$i]->getNom().'</option>';
        }
        
        // Vérifie si toutes les cases sont rempli
        $params['html'] = '';
        if (isset($_POST['Produit'])){
            if(!empty($_POST['NomProduit']) && !empty($_POST['PrixProduit']) && isset($_POST['CategProduit']) && !empty($_POST['ImageProduit']) && !empty($_POST['StockProduit']) && !empty($_POST['DescpProduit']) && !empty($_POST['StockAlertProduit'])){
                $Venteflash = NULL;
                if(isset($_POST['FlashVente'])){
                    $Venteflash = 1;
                }
                DBManagerProduits::AjouterProduit(htmlspecialchars($_POST['NomProduit']), htmlspecialchars($_POST['PrixProduit']), $_POST['CategProduit'], "/Images/" . $_POST['ImageProduit'], htmlspecialchars($_POST['StockProduit']), htmlspecialchars($_POST['DescpProduit']), $Venteflash, htmlspecialchars($_POST['StockAlertProduit']));
            }
            else{
                $params['html'] .= '<p class="text-center" style="color: red;">Veuillez remplir toutes les cases*</p>';
            }
        }

        
        // Affiche la vue
        $view = ROOT."/view/BackOffice/gestionproduits.php";
        self::render($view, $params);
    }

}
?>
