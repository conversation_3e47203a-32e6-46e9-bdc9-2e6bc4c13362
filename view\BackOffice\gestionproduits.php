<!DOCTYPE html>
<html lang="fr-FR">
    <head>
        <title>CSGOSHOP</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <!-- inclus bootstrap via CDN = Content Delivery Network (réseau de distribution de contenu) -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <!-- inclus style.css -->
        <link href="<?php ROOT?>/view/CSS/style.css" rel="stylesheet">

    </head>
    <body class="custom-body">

        <!-- bouton retour en haut de la page -->
        <div class="container mt-3">
            <a href="/BO-Admin/Accueil/" class="btn-return">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-left" viewBox="0 0 16 16">
                    <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8"/>
                </svg>
                <span class="fw-semibold">Retour à l'accueil</span>
            </a>
        </div>

    <form class="" action="" method="POST">
        <div class="container">
            <div class="row">
                <div class="col mt-4">
                    <h1>Produits en ligne</h1>
                    <!-- Bouton pour réinitialiser les produits (Fixtures des produits)-->
                    <div class="box-perso container mt-4">
                        <button type="submit" name="ResetProduits" class="btn btn-outline-dark mb-2">Réinitialiser les produits</boutton>
                    </div>
                    <!-- Tableau des produits -->
                    <table class="table table-striped tablep mt-4">
                        <thead>
                            <th>idProduit</th>
                            <th>Nom</th>
                            <th>Prix Unitaire</th>
                            <th>Catégorie</th>
                            <th>VenteFlash</th>
                            <th>Stock</th>
                            <th>Stock Limite</th>
                            <th></th>
                        </thead>
                        <tbody>
                            <?php
                                $html = '';
                                foreach (DBManagerProduits::getLesProduits() as $produit) {
                                    $html .= '<tr>';
                                    $html .= '<td>' . $produit->getID() . '</td>';
                                    $html .= '<td>' . $produit->getNom() . '</td>';
                                    $html .= '<td>' . $produit->getPrixUnit() . '</td>';
                                    $html .= '<td>' . $produit->getCodeCateg() . '</td>';

                                    // Si le produit est une venteflash alors on affiche oui sinon on affiche non
                                    if($produit->getVenteFlash() == 1){
                                        $html .= '<td>Oui</td>';
                                    } else{
                                        $html .= '<td>Non</td>';
                                    }

                                    $html .= '<td>' . $produit->getStock() . '</td>';
                                    $html .= '<td>' . $produit->getStockAlert() . '</td>';
                                    $html .= '<td><button type="button" class="btn btn-outline-danger" onclick="confirmDelete(\'' . $produit->getNom() . '\')"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-trash-fill" viewBox="0 0 16 19">
                                            <path d="M2.5 1a1 1 0 0 0-1 1v1a1 1 0 0 0 1 1H3v9a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V4h.5a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1H10a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1zm3 4a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-1 0v-7a.5.5 0 0 1 .5-.5M8 5a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-1 0v-7A.5.5 0 0 1 8 5m3 .5v7a.5.5 0 0 1-1 0v-7a.5.5 0 0 1 1 0"/>
                                            </svg></button></td>';
                                    

                                    $html .= '</tr>';
                                }
                                echo $html;
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="row">
                <div class="col mt-4">
                    <h1>Gestion des stocks des produits</h1>
                    <div class="box-perso container mt-4">
                        <p>Sélectionnez un produit : </p>
                        <div class="row">
                            <div class="col-3">
                                <select id="produitselect" name="Selectproduits" style="width: 100%;" class="form-select" aria-label="Default select example">
                                    <?php echo $params['htmloptionselect'] ?>
                                </select>
                            </div>
                            <div class="col-3">
                                <input name="NombreStock" style="width: 100%;" type="text" class="form-control" placeholder="Insérez le stock à modifier">
                            </div>
                            <div class="col-3">
                                <button type="submit" class="btn btn-outline-dark mb-2" name="Bouton" value="Modifier">Modifier</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col mt-4">
                    <h1>Ajouter un produit</h1>
                    <div class="box-perso container mt-4">
                        <?php
                            echo $params['html'];
                        ?>
                        <div class="row">
                            <div class="col-3">
                                <label for="NomProduit" class="form-label">Saisissez un nom : </label>
                                <input name="NomProduit" style="width: 100%;" type="text" class="form-control" placeholder="Nom">
                            </div>
                            <div class="col-3">
                                <label for="PrixProduit" class="form-label">Saisissez le prix : </label>
                                <input name="PrixProduit" style="width: 100%;" type="text" class="form-control" placeholder="Prix">
                            </div>
                            <div class="col-3">
                                <label for="CategProduit" class="form-label">Indiquez la catégorie du produit : </label>
                                <select name="CategProduit" style="width: 100%;" class="form-select" aria-label="Default select example">
                                    <?php echo $params['htmloptionselectcateg'] ?>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-3">
                                <div class="mb-3">
                                    <label for="formFile" class="form-label">Séléctionner une image (PNG/JPG) : </label>
                                    <input name="ImageProduit" class="form-control" type="file">
                                </div>
                            </div>
                            <div class="col-3">
                                <label for="StockProduit" class="form-label">Indiquez le nombre de stock : </label>
                                <input name="StockProduit" style="width: 100%;" type="text" class="form-control" placeholder="Stock">
                            </div>
                            <div class="col-3">
                                <label for="StockAlertProduit" class="form-label">Indiquez le nombre du Stock Limite : </label>
                                <input name="StockAlertProduit" style="width: 100%;" type="text" class="form-control" placeholder="Stock Limite">
                            </div>
                            <div class="col-3">
                                
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="FlashVente" value="Oui" id="flexRadioDefault1">
                                    <label class="form-check-label" for="flexRadioDefault1">
                                    Vente Flash
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-8">
                                <div class="form-floating">
                                    <textarea name="DescpProduit" class="form-control" placeholder="Insérez une description." style="height: 100px"></textarea>
                                    <label for="DescpProduit">Description</label>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-3">
                                <button type="submit" class="btn btn-outline-dark mb-2" name="Produit" value="AjouterProduit">Ajouter</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>



        <!-- Chargement javascript -->
        <!-- js pour bootstrap -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script>
        <!-- js text file -->
        <script src="<?php ROOT?>/Js/backOffice.js"></script>

    </body>
</html>
