/* Progress Steps */
.progress-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 800px;
    margin: 3rem auto;
    position: relative;
    padding: 0 20px;
}

.progress-steps::before {
    content: '';
    position: absolute;
    top: 35px;
    left: 0;
    width: 100%;
    height: 3px;
    background: #e0e0e0;
    z-index: 1;
}

.progress-steps::after {
    content: '';
    position: absolute;
    top: 35px;
    left: 0;
    width: 33%;
    height: 3px;
    background: linear-gradient(65deg, #383F51, #1d0feb);
    z-index: 1;
    transition: width 0.3s ease;
}

.step {
    position: relative;
    z-index: 2;
    width: 70px;
    text-align: center;
}

.step-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    border: 3px solid #e0e0e0;
    transition: all 0.3s ease;
    font-size: 1.5rem;
}

.step.active .step-icon {
    border-color: #1d0feb;
    background: linear-gradient(65deg, #383F51, #1d0feb);
    color: white;
    box-shadow: 0 0 20px rgba(29, 15, 235, 0.3);
}

.step-label {
    margin-top: 1rem;
    font-weight: 600;
    color: #383F51;
    font-size: 0.9rem;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.step.active .step-label {
    opacity: 1;
    color: #1d0feb;
}

/* Bouton de suppression */
.item-remove {
    background: none;
    border: 2px solid #ff4757;
    color: #ff4757;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: inline-flex;  /* Changé en inline-flex */
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    cursor: pointer;
    padding: 0;
    margin: 0;
}

.item-remove i {
    font-size: 1.2rem;  /* Taille ajustée */
    line-height: 1;     /* Ajouté */
    display: block;     /* Ajouté */
}

.item-remove:hover {
    background-color: #ff4757;
    color: white;
    transform: scale(1.05);
}

.item-remove:active {
    transform: scale(0.95);
}

/* Animation de suppression */
.item-remove:active {
    transform: scale(0.95);
}

/* Cart Container */
.cart-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 1rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    margin-bottom: 2rem;
}

.cart-item {
    display: grid;
    grid-template-columns: auto 1fr auto auto auto;
    gap: 1.5rem;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.item-image img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 0.5rem;
}

.item-details h3 {
    margin: 0;
    font-size: 1.1rem;
    color: #383F51;
}

.item-quantity {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.qty-btn {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: none;
    background: #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.qty-btn:hover {
    background: #383F51;
    color: white;
}

.item-total {
    font-weight: bold;
    color: #383F51;
}

.cart-summary {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 2px solid rgba(0,0,0,0.1);
}

.summary-row {
    display: flex;
    justify-content: space-between;
    font-size: 1.2rem;
    font-weight: bold;
    color: #383F51;
}

/* Navigation Buttons */
.navigation-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 2rem;
}

.btn-outline {
    border: 2px solid #383F51;
    color: #383F51;
    padding: 0.8rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.btn-outline:hover {
    background: #383F51;
    color: white;
}

.btn-primary {
    background: linear-gradient(65deg, #383F51, #1d0feb);
    border: none;
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(29, 15, 235, 0.3);
}

/* Empty Cart */
.empty-cart {
    text-align: center;
    padding: 3rem;
}

.empty-cart i {
    font-size: 4rem;
    color: #383F51;
    margin-bottom: 1rem;
}

.empty-cart p {
    color: #666;
    margin-bottom: 1.5rem;
}
