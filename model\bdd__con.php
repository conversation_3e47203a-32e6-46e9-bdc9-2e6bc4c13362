<?php
/* définition des constantes de connexion */

const HOST = "127.0.0.1";
const PORT = "3307"; //port de connexion à MariaDB
const DBNAME = "db_e-commerce";
const CHARSET = "utf8";
const LOGIN = 'root';
const MDP = '';
$dsn = 'mysql:host='.HOST.';port='.PORT.';dbname='.DBNAME.';charset='.CHARSET;

try{
   $cnx = new PDO($dsn, LOGIN, MDP);
   // Gestion des erreurs par exception
   $cnx->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
   //echo'<p>CONNEXION REUSSI</p>';
}
catch (Exception $e){
   die('Erreur : '.$e->getMessage());
}
?>