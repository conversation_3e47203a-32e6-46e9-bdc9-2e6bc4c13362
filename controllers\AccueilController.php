<?php
class AccueilController extends Controller{


    public static function index($params){

        // appelle la vue
        $view = ROOT."/view/Home.php";
        self::render($view, $params);
    }

    /**
     * Action qui rafraîchit les produits séléctionnés
     * params : tableau des paramètres
     */
    public static function selectmulti($params) {
        // Récupère les filtres de l'URL ou définit 'Tous' par défaut
        $filtres = isset($_GET['selectmulti']) ? $_GET['selectmulti'] : ['Tous'];

        // Ajoute les filtres aux paramètres
        $params['filtres'] = $filtres;

        // appelle la vue
        $view = ROOT."/view/Home.php";
        self::render($view, $params);
    }
}
?>
