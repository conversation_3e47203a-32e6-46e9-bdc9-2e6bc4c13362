        <!-- Barre de navigation de la page d'acceuil -->
        <nav class="navbar navbar-expand-lg background">
            <div class="container-fluid">
                <a class="navbar-brand ms-3" href="/"><img src="<?php ROOT?>/public/Images/CSGOSHOP LOGO.png" alt="" width="100"></a>
                <div class="collapse navbar-collapse nav-bloc">
                    <!-- Les text-links -->
                    <ul class="nav nav-underline navbar-nav ">
                        <li class="nav-item">
                        <a class="nav-link item-link" aria-current="page" href="/">Accueil</a>
                        </li>
                        <li class="nav-item">
                        <a class="nav-link item-link" href="/VenteFlash/">⚡ Vente Flash</a>
                        </li>
                        <li class="nav-item">
                        <a class="nav-link item-link" href="/MeilleursVentes/">🔥 Meilleurs Ventes</a>
                        </li>
                        <li class="nav-item dropdown">
                    </ul>
                </div>
                <!-- Bouton search -->
                <form method="GET" action="/Recherche/recherche" class="d-flex" role="Search">
                            <input class="form-control me-2 rounded-pill" type="search" name="Mots" placeholder="Recherche" aria-label="Search">
                            <button class="btn btn-outline-light rounded-pill" type="submit">Recherche</button>
                </form>
                <!-- Bouton Panier (lance un modal) -->
                 <?php
                        if(isset($_SESSION['user'])){
                            echo '<a href="" class="btn btn-outline-dark mx-2" data-bs-toggle="modal" data-bs-target="#exampleModal"><svg xmlns="http://www.w3.org/2000/svg" width="30" height="25" fill="currentColor" class="bi bi-cart" viewBox="0 0 16 18">
                            <path d="M0 1.5A.5.5 0 0 1 .5 1H2a.5.5 0 0 1 .485.379L2.89 3H14.5a.5.5 0 0 1 .491.592l-1.5 8A.5.5 0 0 1 13 12H4a.5.5 0 0 1-.491-.408L2.01 3.607 1.61 2H.5a.5.5 0 0 1-.5-.5M3.102 4l1.313 7h8.17l1.313-7zM5 12a2 2 0 1 0 0 4 2 2 0 0 0 0-4m7 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4m-7 1a1 1 0 1 1 0 2 1 1 0 0 1 0-2m7 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2"/>
                            </svg></a>';
                        }
                        else{
                            echo '<a href="/Connexion/" class="btn btn-outline-dark mx-2" data-bs-toggle="modal" data-bs-target="#exampleModal"><svg xmlns="http://www.w3.org/2000/svg" width="30" height="25" fill="currentColor" class="bi bi-cart" viewBox="0 0 16 18">
                            <path d="M0 1.5A.5.5 0 0 1 .5 1H2a.5.5 0 0 1 .485.379L2.89 3H14.5a.5.5 0 0 1 .491.592l-1.5 8A.5.5 0 0 1 13 12H4a.5.5 0 0 1-.491-.408L2.01 3.607 1.61 2H.5a.5.5 0 0 1-.5-.5M3.102 4l1.313 7h8.17l1.313-7zM5 12a2 2 0 1 0 0 4 2 2 0 0 0 0-4m7 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4m-7 1a1 1 0 1 1 0 2 1 1 0 0 1 0-2m7 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2"/>
                            </svg></a>';
                        }
                ?>
                <!-- Bouton Compte -->
                    <?php
                        if(isset($_SESSION['user'])){
                                echo '<div class="btn-group">
                                <button type="button" class="btn btn-outline-light me-2 dropdown-toggle" data-bs-toggle="dropdown" data-bs-display="static" aria-expanded="false"><svg xmlns="http://www.w3.org/2000/svg" width="30" height="26" fill="currentColor" class="bi bi-person-circle" viewBox="0 0 16 16">
                                <path d="M11 6a3 3 0 1 1-6 0 3 3 0 0 1 6 0"/>
                                <path fill-rule="evenodd" d="M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8m8-7a7 7 0 0 0-5.468 11.37C3.242 11.226 4.805 10 8 10s4.757 1.225 5.468 2.37A7 7 0 0 0 8 1"/>
                                </svg></button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item dropdown-color-hover" href="/MonProfil/">Mon profil</a></li>
                                    <li><form method="post" action="" style="margin: 0;">
                                    <button type="submit" class="dropdown-item" name="deconnexion">Se déconnecter</button>
                                    </form></li>
                                </ul></div>'.'<p style="font-size: 13px; color:white;">'.$_SESSION['user'].'</p>';
                                if (isset($_POST['deconnexion'])) {
                                    header("Location: /Connexion/");
                                    session_destroy();
                                    exit();
                                }
                        }
                        else{
                            echo '<a href="/Connexion/" class="btn btn-outline-light me-2"><svg xmlns="http://www.w3.org/2000/svg" width="30" height="26" fill="currentColor" class="bi bi-person-circle" viewBox="0 0 16 16">
                            <path d="M11 6a3 3 0 1 1-6 0 3 3 0 0 1 6 0"/>
                            <path fill-rule="evenodd" d="M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8m8-7a7 7 0 0 0-5.468 11.37C3.242 11.226 4.805 10 8 10s4.757 1.225 5.468 2.37A7 7 0 0 0 8 1"/>
                            </svg></a>';
                        }
                    ?>
            </div>
        </nav>
