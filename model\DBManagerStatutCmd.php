<?php
class DBManagerStatutCmd {
    private static ?\PDO $cnx;
    private static array $StatutCmds = array();
    private static StatutCmd $StatutCmd;

    public function __construct(){
        self::$StatutCmd = new StatutCmd(0,'');
    }

    /**
     * Permet de récupérer un statutCmd de par un id
     * 
     */
    public static function getStatutCmdById($id) {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "SELECT Id_Statut_Cmd, libelle FROM statut_cmd WHERE Id_Statut_Cmd = ?";

        // Préparer la requête
        $sql = self::$cnx->prepare($query);

        // Exécuter la requête
        $sql->execute([$id]);

        // Récupérer le statutCmd
        $row = $sql->fetch();

        if ($row) {
            self::$StatutCmd = new StatutCmd($row['Id_Statut_Cmd'], $row['libelle']);
        }

        return self::$StatutCmd;
    }


}
?>