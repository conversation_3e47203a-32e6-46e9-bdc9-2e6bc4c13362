<?php
class PageSecuriteController extends Controller{


    public static function index($params){

        // Vérifie si l'utilisateur est connecté en tant qu'user
        if (!isset($_SESSION['user'])) {
            // Redirige vers la page de connexion si non connecté
            header("Location: /Connexion/");
            exit();
        }
        
        $params['html'] = '';

        if (isset($_SESSION['user'])) {

            $params['user'] = DBManagerClient::getClientByName2(htmlspecialchars($_SESSION['user']));
            $params['username'] = $params['user']->getUtilisateur();
            $params['prenom'] = $params['user']->getPrenom();
            $params['nom'] = $params['user']->getNom();
            $params['email'] = $params['user']->getEmail();
            $params['mobile'] = $params['user']->getTel();
            $params['password'] = $params['user']->getMdp();

            if ($_SERVER['REQUEST_METHOD'] == 'POST') {
                $updated = false;

                // Vérifie et met à jour l'utilisateur uniquement si une valeur a été saisie
                if(!empty($_POST['User']) && $_POST['User'] !== $params['username']) {
                    $utilisateur = htmlspecialchars($_POST['User']);
                    DBManagerClient::ModifUserNameClient($utilisateur, $_SESSION['user']);
                    $_SESSION['user'] = $utilisateur; // Met à jour la session avec le nouveau nom d'utilisateur
                    $updated = true;
                }

                // Vérifie et met à jour le prénom uniquement si une valeur a été saisie
                if(!empty($_POST['Prenom']) && $_POST['Prenom'] !== $params['prenom']) {
                    $prenom_uti = htmlspecialchars($_POST['Prenom']);
                    DBManagerClient::ModifFirstNameClient($prenom_uti, $_SESSION['user']);
                    $updated = true;
                }

                // Vérifie et met à jour le nom uniquement si une valeur a été saisie
                if(!empty($_POST['Nom']) && $_POST['Nom'] !== $params['nom']) {
                    $nom_uti = htmlspecialchars($_POST['Nom']);
                    DBManagerClient::ModifNameClient($nom_uti, $_SESSION['user']);
                    $updated = true;
                }

                // Vérifie et met a jour l'email uniquement si une valeur a été saisie
                if(!empty($_POST['email']) && $_POST['email'] !== $params['email']) {
                    $email_uti = htmlspecialchars($_POST['email']);
                    DBManagerClient::ModifEmailClient($email_uti, $_SESSION['user']);
                    $updated = true;
                }

                // Vérifie et met à jour le téléphone uniquement si une valeur a été saisie
                if(!empty($_POST['mobile']) && $_POST['mobile'] !== $params['mobile']) {
                    $mobile_uti = htmlspecialchars($_POST['mobile']);
                    DBManagerClient::ModifMobileClient($mobile_uti, $_SESSION['user']);
                    $updated = true;
                }

                // Vérifie et met a jour le mot de passe uniquement si une valeur a été saisie et confirmé
                if(!empty($_POST['mdp2']) && !empty($_POST['mdpconfirmation'])) {
                    $mdp_uti = htmlspecialchars($_POST['mdp2']);
                    $mdpconf_uti = htmlspecialchars($_POST['mdpconfirmation']);
                    if($mdp_uti === $mdpconf_uti) {
                        DBManagerClient::ModifPasswordClient($mdp_uti, $_SESSION['user']);
                        $updated = true;
                    }
                }

                if($updated) {
                    $params['html'] .= '<p class="fs-5 text-center" style="color: green;">Modification effectuée avec succès !</p>';
                } else {
                    $params['html'] .= '<p class="fs-5 text-center" style="color: red;">Aucune modification n\'a été apportée.</p>';
                }
            }
        }
        
        // appelle la vue
        $view = ROOT."/view/Compte/pagesecurite.php";
        self::render($view, $params);
    }
}
?>