<?php
class DBManagerCommande {
    private static ?\PDO $cnx;
    private static Commande $commande;
    private static array $LesCommandes;

    public function __construct(){
        self::$commande = new Commande(0, "", "", 0, 0);
    }

    /**
     * Permet de récupérer une commande dans une base de donnée de par un id
     * 
     */
    public static function getLesCommandes() {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Initialiser un tableau pour stocker les Commandes
        self::$LesCommandes = [];

        // Requête SQL
        $query = "SELECT RefCmd, dateCmd, dateLiv, Id_mode_paiement, idClient FROM Commandes";

        // Préparer la requête
        $sql = self::$cnx->prepare($query);

        // Exécuter la requête
        $sql->execute();

        // Récupérer les Commandes
        while ($row = $sql->fetch(PDO::FETCH_ASSOC)) {
            self::$commande = new Commande($row['RefCmd'], $row['dateCmd'], $row['dateLiv'], $row['Id_mode_paiement'], $row['idClient']);
            self::$LesCommandes[] = self::$commande;
        }

        return self::$LesCommandes;
    }

    /**
     * Insère une commande dans la table Commandes
     */
    public static function InsertCommande($dateCmd, $dateLiv, $id_mode_paiement, $idClient) {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "INSERT INTO Commandes(dateCmd, dateLiv, Id_mode_paiement, idClient) VALUES(?, ?, ?, ?)";

        // Préparer la requête
        $sql = self::$cnx->prepare($query);

        // Exécuter la requête
        return $sql->execute([$dateCmd, $dateLiv, $id_mode_paiement, $idClient]);
    }

    /**
     * Récupère le dernier ID de la table Commandes
     */
    public static function getLastId() {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "SELECT MAX(RefCmd) FROM Commandes";

        // Préparer la requête
        $sql = self::$cnx->prepare($query);

        // Exécuter la requête
        $sql->execute();

        // Récupérer le dernier ID
        $lastId = $sql->fetchColumn();

        return $lastId;
    }

    /**
     * Récupère une ou plusieurs commande par l'id du client
     */
    public static function getCommandeByIdClient($id) {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "SELECT RefCmd, dateCmd, dateLiv, Id_mode_paiement, idClient FROM Commandes WHERE idClient = ?";
        
        // Préparer la requête
        $sql = self::$cnx->prepare($query);

        // Exécuter la requête
        $sql->execute([$id]);

        // Récupérer les Commandes
        while ($row = $sql->fetch(PDO::FETCH_ASSOC)) {
            self::$commande = new Commande($row['RefCmd'], $row['dateCmd'], $row['dateLiv'], $row['Id_mode_paiement'], $row['idClient']);
            self::$LesCommandes[] = self::$commande;
        }

        return self::$LesCommandes;
    }



}