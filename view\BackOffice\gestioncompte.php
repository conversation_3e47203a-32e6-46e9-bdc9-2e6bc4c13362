<!DOCTYPE html>
<html lang="fr-FR">
    <head>
        <title>CSGOSHOP</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <!-- inclus bootstrap via CDN = Content Delivery Network (réseau de distribution de contenu) -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <!-- inclus style.css -->
        <link href="<?php ROOT?>/view/CSS/style.css" rel="stylesheet">

    </head>
    <body class="custom-body">

        <!-- bouton retour en haut de la page -->
        <div class="container mt-3">
            <a href="/BO-Admin/Accueil/" class="btn-return">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-left" viewBox="0 0 16 16">
                    <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8"/>
                </svg>
                <span class="fw-semibold">Retour à l'accueil</span>
            </a>
        </div>

    <!-- Gestion des admins -->
    <form class="content" action="" method="POST">
        <div class="container">
            <div class="row">
                <!-- Tableau des admins du site -->
                <div class="col mt-4 me-4">
                    <h1>Administrateur</h1>
                    <table class="table table-striped tablep mt-4">
                        <thead>
                            <th>idClient</th>
                            <th>Utilisateur</th>
                            <th>Nom</th>
                            <th>Prénom</th>
                            <th>Mobile</th>
                            <th>Email</th>
                            <th></th>
                        </thead>
                        <tbody>
                            <?php
                                $html = '';
                                foreach (DBManagerAdmin::getAdmins() as $admin) {
                                    $html .= '<tr>';
                                    $html .= '<td>' . $admin->getID() . '</td>';
                                    $html .= '<td>' . $admin->getUtilisateur() . '</td>';
                                    $html .= '<td>' . $admin->getNom() . '</td>';
                                    $html .= '<td>' . $admin->getPrenom() . '</td>';
                                    $html .= '<td>' . $admin->getTel() . '</td>';
                                    $html .= '<td>' . $admin->getEmail() . '</td>';
                                    $html .= '<td><button type="button" class="btn btn-outline-danger"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-trash-fill" viewBox="0 0 16 19">
                                            <path d="M2.5 1a1 1 0 0 0-1 1v1a1 1 0 0 0 1 1H3v9a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V4h.5a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1H10a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1zm3 4a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-1 0v-7a.5.5 0 0 1 .5-.5M8 5a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-1 0v-7A.5.5 0 0 1 8 5m3 .5v7a.5.5 0 0 1-1 0v-7a.5.5 0 0 1 1 0"/>
                                            </svg></button></td>';
                                    $html .= '</tr>';
                                }
                                echo $html;
                            ?>
                        </tbody>
                    </table>
                </div>
                <!-- Gestion des privilèges des admins de la bdd -->
                <div class="col mt-4">
                    <h1>Gestion de la BDD</h1>
                    <div class="box-perso container mt-4">
                        <?php echo $params['html']; ?>
                        <p>Sélectionnez un Administrateur : </p>
                        <?php


                        // Parcours toutes la liste des admins de la BDD et récupère le nom d'utilisateur dans un tableau
                        $Tabadmin = [];
                        foreach(DBManagerAdmindb::getAccountAdmin() as $admindb){
                            $Tabadmin[] = $admindb;
                        }

                        // Parcours tous les admins du tableau et le met dans bouton défilant
                        $htmloptionselect = '<option value="Aucun" disabled>Aucun</option>';
                        for($i=0; $i<count($Tabadmin); $i++){
                            $htmloptionselect .= '<option value="'.$Tabadmin[$i]->getNom().'">'.$Tabadmin[$i]->getNom().'</option>';
                        }

                        ?>
                        <select id="AdminSelect" name="AdminSelect" style="width: 35%;" class="form-select mt-2 mb-4" aria-label="Default select example">
                            <?php echo $htmloptionselect ?>
                        </select>
                        <p>Choisissez les droits de la BDD de cette Administrateur : </p>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="READ" id="readCheck" name="readCheck">
                            <label class="form-check-label" for="readCheck">
                                READ
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="UPDATE" id="updateCheck" name="updateCheck">
                            <label class="form-check-label" for="updateCheck">
                                UPDATE
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="CREATE" id="createCheck" name="createCheck">
                            <label class="form-check-label" for="createCheck">
                                CREATE
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="DELETE" id="deleteCheck" name="deleteCheck">
                            <label class="form-check-label" for="deleteCheck">
                                DELETE
                            </label>
                        </div>
                        <div class="align-self-center d-flex justify-content-center">
                            <button style="width: 25%;" type="submit" class="btn btn-outline-dark mt-3 mx-auto p-2" name="GestionPrivileges" value="Validé">Valider</button>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </form>

        <!-- Chargement javascript -->
        <!-- js pour bootstrap -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script>
        <!-- js text file -->
        <script src="<?php ROOT; ?>/Js/ajaxbackoffice.js"></script>

    </body>
</html>

