.confirmation-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 2rem;
}

.confirmation-card {
    background-color: white;
    border-radius: 20px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    padding: 3rem;
    max-width: 800px;
    width: 100%;
    text-align: center;
    transition: transform 0.3s ease;
}

.confirmation-card:hover {
    transform: translateY(-5px);
}

.confirmation-icon {
    font-size: 5rem;
    color: #28a745;
    margin-bottom: 1.5rem;
}

.confirmation-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #333;
}

.confirmation-text {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.btn-home {
    background: linear-gradient(65deg, #383F51, #1d0feb);
    color: white;
    border: none;
    padding: 0.8rem 2rem;
    font-size: 1.1rem;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.btn-home:hover {
    background: linear-gradient(65deg, #1d0feb, #383F51);
    color: white;
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(29, 15, 235, 0.3);
}

.animate-in {
    animation: fadeIn 1s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}