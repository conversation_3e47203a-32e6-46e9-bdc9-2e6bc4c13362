function ModifierQtePanier(idProduit) {
    const produits = document.getElementsByClassName('NomProduit');
    //Affiche un input dans la cellule de la quantité dans la ligne du produit
    for (let i = 0; i < produits.length; i++) {
        if (produits[i].dataset.id == idProduit) {
            produits[i].parentElement.querySelector('.QteProduit').innerHTML = '<input class="" type="number" name="qte"><button type="submit" name="ModifierQtePanier" value="' + idProduit + '">Valider</button>';
        }
    }
}


