function ModifierQtePanier(idProduit) {
    const produits = document.getElementsByClassName('NomProduit');
    //Affiche un input dans la cellule de la quantité dans la ligne du produit
    for (let i = 0; i < produits.length; i++) {
        if (produits[i].dataset.id == idProduit) {
            produits[i].parentElement.querySelector('.QteProduit').innerHTML = '<input class="form-control" type="number" name="qte" style="width: 80px; display: inline-block;"><button class="btn btn-primary ms-1" type="submit" name="ModifierQtePanier" value="' + idProduit + '">Valider</button>';
        }
    }
}


