/* Style pour la page de sécurité admin */
.custom-body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

.page-wrapper {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Titre principal */
.container h1 {
    color: #2d3436;
    font-size: 2.2rem;
    margin-bottom: 1.5rem;
    font-weight: 600;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    display: inline-block;
}

.container h1::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #0984e3, #acd6ff, #0984e3);
    background-size: 200% 100%;
    border-radius: 2px;
    animation: underlineAnimate 2s linear infinite;
}

@keyframes underlineAnimate {
    0% {
        background-position: 100% 0;
    }
    100% {
        background-position: -100% 0;
    }
}

/* Bouton retour */
.underline-change {
    text-decoration: none;
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 8px 16px;
    border-radius: 8px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.7);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.underline-change:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Style du formulaire */
.shadow-sm {
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 16px !important;
    padding: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
}

.shadow-sm:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
}

.shadow-sm h1 {
    color: #0984e3;
    font-size: 1.8rem;
    margin-bottom: 20px;
    text-align: center;
}

/* Style des champs de formulaire */
.form-control {
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 12px;
    transition: all 0.3s ease;
    background: white;
}

.form-control:focus {
    border-color: #0984e3;
    box-shadow: 0 0 0 3px rgba(9, 132, 227, 0.25);
}

/* Style des boutons */
.btn-outline-dark {
    border: 2px solid #0984e3;
    color: #0984e3;
    border-radius: 8px;
    padding: 8px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-outline-dark:hover {
    background: #0984e3;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(9, 132, 227, 0.3);
}

/* Style du modal */
.modal-content {
    border-radius: 16px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
    background: linear-gradient(135deg, #0984e3, #74b9ff);
    color: white;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    border-bottom: none;
}

.modal-title {
    font-weight: 600;
}

.modal-body {
    padding: 20px;
}

/* Style des messages de succès et d'erreur */
.success-message {
    color: #2ecc71;
    font-weight: 500;
    text-align: center;
    padding: 10px;
    background: rgba(46, 204, 113, 0.1);
    border-radius: 8px;
    margin-bottom: 15px;
}

.error-message {
    color: #e74c3c;
    font-weight: 500;
    text-align: center;
    padding: 10px;
    background: rgba(231, 76, 60, 0.1);
    border-radius: 8px;
    margin-bottom: 15px;
}

/* Style des labels */
label {
    color: #2d3436;
    font-weight: 600;
    margin-bottom: 8px;
}

/* Animation pour les champs de formulaire */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.mt-2 {
    animation: fadeIn 0.5s ease forwards;
}

.mt-2:nth-child(1) { animation-delay: 0.1s; }
.mt-2:nth-child(2) { animation-delay: 0.2s; }
.mt-2:nth-child(3) { animation-delay: 0.3s; }
.mt-2:nth-child(4) { animation-delay: 0.4s; }
.mt-2:nth-child(5) { animation-delay: 0.5s; }