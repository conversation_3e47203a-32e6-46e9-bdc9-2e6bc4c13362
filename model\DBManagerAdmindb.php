<?php
class DBManagerAdmindb {
    private static ?\PDO $cnx;
    private static AdminDb $adminDb;
    private static array $LesAdmins;

    public function __construct(){
        self::$adminDb = new AdminDb("", "");
    }

    /**
     * Permet de récupérer les comptes admins de la BDD
     */

     public static function getAccountAdmin(){
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        //Initialise le tableau des Admin
        self::$LesAdmins = [];

        // Requête SQL
        $query = "SELECT User, Host FROM mysql.user";

        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);

        // Exécuter la requête
        $sql1->execute();

        while($row = $sql1->fetch(PDO::FETCH_ASSOC)) {
            $admins = new AdminDb($row['User'], $row['Host']);
            self::$LesAdmins[] = $admins;
        }

        return self::$LesAdmins;
    }


    /**
     * Permet de récupérer les privilèges des comptes admins de la BDD
     */

     public static function getPrivilegesAccountAdmins($user, $host) {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();
    
        $sql = "SHOW GRANTS FOR ? @ ?";

        $stmt = self::$cnx->prepare($sql);

        $stmt->execute([$user, $host]);

        $grants = $stmt->fetchAll(PDO::FETCH_COLUMN);

        $privs = [];
        foreach ($grants as $g) {
            if (strpos($g, 'SELECT') !== false) {
                $privs[] = 'SELECT';
            }
            if (strpos($g, 'CREATE') !== false) {
                $privs[] = 'CREATE';
            }
            if (strpos($g, 'UPDATE') !== false) {
                $privs[] = 'UPDATE';
            }
            if (strpos($g, 'DELETE') !== false) {
                $privs[] = 'DELETE';
            }
            if (strpos($g, 'ALL PRIVILEGES') !== false) {
                $privs[] = 'ALL PRIVILEGES';
            }
        }
        $privs = array_values(array_unique($privs));

        return $privs;
    }
    
    /**
     * Permet de modifier les privilèges des comptes admins de la BDD
     */ 

     public static function updatePrivilegesAccountAdmins($user, $host, $privileges) {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        $sql = "REVOKE ALL PRIVILEGES, GRANT OPTION FROM ? @ ?;";

        foreach ($privileges as $privilege) {
            $sql .= " GRANT $privilege ON *.* TO '$user'@'$host'; ";
        }

        $sql .= "FLUSH PRIVILEGES;";

        $stmt = self::$cnx->prepare($sql);

        $stmt->execute();
        
    }

    /**
     * Permet de récupérer les privilèges d'un compte administrateur d'une table
     */

     public static function getPrivilegesAccountAdminsForEachTable($user, $table, $host) {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();
    
        $sql = "SHOW GRANTS FOR '$user'@'$host'";

        $stmt = self::$cnx->prepare($sql);

        $stmt->execute();

        $grants = $stmt->fetchAll(PDO::FETCH_COLUMN);

        $privs = [];
        foreach ($grants as $g) {
            if (strpos($g, 'SELECT') !== false) {
                $privs[] = 'SELECT';
            }
            if (strpos($g, 'CREATE') !== false) {
                $privs[] = 'CREATE';
            }
            if (strpos($g, 'UPDATE') !== false) {
                $privs[] = 'UPDATE';
            }
            if (strpos($g, 'DELETE') !== false) {
                $privs[] = 'DELETE';
            }
            if (strpos($g, 'ALL PRIVILEGES') !== false) {
                $privs[] = 'ALL PRIVILEGES';
            }
        }
        $privs = array_values(array_unique($privs));

        return $privs;
    }
}
?>