<?php
class DBManagerAdmindb {
    private static ?\PDO $cnx;
    private static AdminDb $adminDb;
    private static array $LesAdmins;

    public function __construct(){
        self::$adminDb = new AdminDb("", "");
    }

    /**
     * Permet de récupérer les comptes admins de la BDD
     */

     public static function getAccountAdmin(){
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        //Initialise le tableau des Admin
        self::$LesAdmins = [];

        // Requête SQL
        $query = "SELECT User, Host FROM mysql.user";

        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);

        // Exécuter la requête
        $sql1->execute();

        while($row = $sql1->fetch(PDO::FETCH_ASSOC)) {
            $admins = new AdminDb($row['User'], $row['Host']);
            self::$LesAdmins[] = $admins;
        }

        return self::$LesAdmins;
    }


    /**
     * Permet de récupérer les privilèges des comptes admins de la BDD
     */

     public static function getPrivilegesAccountAdmins($user, $host) {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();
    
        $sql = "SHOW GRANTS FOR ?@?";

        $stmt = self::$cnx->prepare($sql);

        $stmt->execute([$user, $host]);

        $grants = $stmt->fetchAll(PDO::FETCH_COLUMN);

        $privs = [];
        foreach ($grants as $g) {
            if (strpos($g, 'SELECT') !== false) {
                $privs[] = 'SELECT';
            }
            if (strpos($g, 'CREATE') !== false) {
                $privs[] = 'CREATE';
            }
            if (strpos($g, 'UPDATE') !== false) {
                $privs[] = 'UPDATE';
            }
            if (strpos($g, 'DELETE') !== false) {
                $privs[] = 'DELETE';
            }
            if (strpos($g, 'ALL PRIVILEGES') !== false) {
                $privs[] = 'ALL PRIVILEGES';
            }
        }
        $privs = array_values(array_unique($privs));

        return $privs;
    }
    
    /**
     * Permet de modifier les privilèges des comptes admins de la BDD
     */ 

     public static function updatePrivilegesAccountAdmins($user, $host, $privileges) {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        $sql = "REVOKE ALL PRIVILEGES, GRANT OPTION FROM ?@?;";

        $params = [$user, $host];

        foreach ($privileges as $privilege) {
            $sql .= " GRANT $privilege ON *.* TO ?@?; ";
            $params[] = $user; 
            $params[] = $host;
        }

        $sql .= "FLUSH PRIVILEGES;";

        $stmt = self::$cnx->prepare($sql);

        $stmt->execute($params);
        
    }

    /**
     * Permet de récupérer les privilèges d'un compte administrateur d'une table
     */

     public static function getPrivilegesAccountAdminsForEachTable($user, $table, $host) {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();
    
        $sql = "SHOW GRANTS FOR ?@?";

        $stmt = self::$cnx->prepare($sql);

        $stmt->execute([$user, $host]);

        $grants = $stmt->fetchAll(PDO::FETCH_COLUMN);

        $privs = [];
        foreach ($grants as $g) {
            if(strpos($g, $table) !== false || strpos($g, '.*') !== false){
                if (strpos($g, 'SELECT') !== false) {
                    $privs[] = 'SELECT';
                }
                if (strpos($g, 'CREATE') !== false) {
                    $privs[] = 'CREATE';
                }
                if (strpos($g, 'UPDATE') !== false) {
                    $privs[] = 'UPDATE';
                }
                if (strpos($g, 'DELETE') !== false) {
                    $privs[] = 'DELETE';
                }
                if (strpos($g, 'ALL PRIVILEGES') !== false) {
                    $privs[] = 'ALL PRIVILEGES';
                }
            }
        }
        $privs = array_values(array_unique($privs));

        return $privs;
    }

    /**
     * Permet de modifier les privilèges des comptes admins de la BDD en fonction d'une table
     */ 

     public static function updatePrivilegesAccountAdminsForEachTable($user, $host, $privileges, $table) {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        $sql = "";

        $params = [];

        //Tableau des privilèges accordés (Pour contrer l'injection SQL)
        $autorisationPrivilege = ['SELECT', 'CREATE', 'UPDATE', 'DELETE'];
        $autorisationTable = DBManagerAdmindb::getTables();

        foreach ($privileges as $privilege) {
            if(in_array('SELECT', $privileges) || in_array('CREATE', $privileges) || in_array('UPDATE', $privileges) || in_array('DELETE', $privileges)){
                $sql = "REVOKE SELECT, CREATE, UPDATE, DELETE ON $table FROM ?@?;";
                $params[] = $user; 
                $params[] = $host;
            }

            //Exception afin d'évité les injections SQL
            if(!in_array($privilege, $autorisationPrivilege)){
                throw new Exception("Privilege non autorisé");
            }
            if(!in_array($table, $autorisationTable)){
                throw new Exception("Table non autorisée");
            }

            $sql .= " GRANT $privilege ON $table TO ?@?; ";
            $params[] = $user; 
            $params[] = $host;
        }

        $sql .= "FLUSH PRIVILEGES;";

        $stmt = self::$cnx->prepare($sql);

        $stmt->execute($params);

        return $sql;
    }

    /**
     * Récupère les tables de la BDD
     */
    public static function getTables() {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        $sql = "SHOW TABLES";

        $stmt = self::$cnx->prepare($sql);

        $stmt->execute();

        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

        return $tables;
    }
}
?>