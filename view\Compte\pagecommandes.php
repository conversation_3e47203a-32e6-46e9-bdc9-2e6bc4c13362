<!DOCTYPE html>
<html lang="fr-FR">
    <head>
        <title>CSGOSHOP</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <!-- inclus bootstrap via CDN = Content Delivery Network (réseau de distribution de contenu) -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <!-- inclus style.css -->
        <link href="<?php ROOT?>/view/CSS/style.css" rel="stylesheet">
        <link href="<?php ROOT?>/view/CSS/panier.css" rel="stylesheet">

    </head>
    <body class="custom-body">
        <!-- Wrapper principal -->
        <div class="page-wrapper">
            <!-- Navbar -->
            <?php include_once ROOT.'/view/template/navbar.php'; ?>

            <!-- Content -->
            <div class="content">
                <!-- Titre -->
                <div class="container">
                    <h1 class="mt-3">Historique des commandes</h1>
                    <div class="col col-lg-3 mt-2">
                        <a href="/MonProfil/" class="text-dark underline-change">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-left" viewBox="0 0 16 18">
                                <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8"/>
                            </svg> Retour
                        </a>
                    </div>
                </div>

                <!-- Table des commandes -->
                <div class="container mt-5">
                    <div class="row">
                        <div class="col-12">
                            <table class="table tablep">
                                <thead>
                                    <tr>
                                        <th scope="col">Statut</th>
                                        <th scope="col">Date de commande</th>
                                        <th scope="col">Date de livraison</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    foreach (DBManagerCommande::getCommandeByIdClient(DBManagerClient::getClientByName2($_SESSION['user'])->getID()) as $commande) {
                                        echo '<tr>';
                                        echo '<td><span class="fw-medium">' . DBManagerStatutCmd::getStatutCmdById(DBManagerAttribuer::getAttribuerByIdCmdRecent($commande->getRefCmd())->getIdStatutCmd())->getLibelle() . '</span></td>';
                                        echo '<td>' . date('d/m/Y', strtotime($commande->getDateCmd())) . '</td>';
                                        
                                        if ($commande->getDateLiv()) {
                                            echo '<td>' . date('d/m/Y', strtotime($commande->getDateLiv())) . '</td>';
                                        } else {
                                            echo '<td>Non définie</td>';
                                        }
                                        
                                        echo '</tr>';
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <?php include_once ROOT.'/view/template/footer.php'; ?>

            <!-- Modal -->
            <?php include_once ROOT.'/view/template/panier.php'; ?>
        </div>

            <!-- Chargement javascript -->
            <!-- js pour bootstrap -->
            <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script>
            <!-- js text file -->
            <script src="<?php ROOT?>/Js/formMdp.js"></script>
            <script src="<?php ROOT?>/Js/ModifQtePanier.js"></script>
        </div>
    </body>
</html>
