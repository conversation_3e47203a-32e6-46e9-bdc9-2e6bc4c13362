/* Style pour les boutons de suppression et d'édition */
.item-remove {
    background: none;
    border: 2px solid #ff4757;
    color: #ff4757;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    cursor: pointer;
    padding: 0;
    margin: 0;
}

.item-remove i {
    font-size: 1.2rem;
    line-height: 1;
    display: block;
}

.item-remove:hover {
    background-color: #ff4757;
    color: white;
    transform: scale(1.05);
}

.item-remove:active {
    transform: scale(0.95);
}

/* Style pour le tableau du panier */
.panier-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    margin: 20px 0;
}

.panier-table thead {
    background: #202020;
    color: #ffffff;
}

.panier-table th {
    padding: 15px;
    text-align: left;
    font-weight: 600;
    font-size: 1rem;
}

.panier-table td {
    padding: 15px;
    border-bottom: 1px solid #f2f2f2;
    vertical-align: middle;
}

.panier-table tbody tr:last-child td {
    border-bottom: none;
}

.panier-table tbody tr:hover {
    background-color: #f8f9fa;
    transition: background-color 0.3s ease;
}

/* Style pour les boutons dans le panier */
.panier-table .btn-delete, .panier-table .btn-edit {
    background: none;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    cursor: pointer;
    padding: 0;
    margin: 0;
}

.panier-table .btn-delete {
    border: 2px solid #dc3545;
    color: #dc3545;
}

.panier-table .btn-delete:hover {
    background: #dc3545;
    color: white;
    transform: scale(1.05);
}

.panier-table .btn-edit {
    border: 2px solid #dcb535;
    color: #dcb535;
}

.panier-table .btn-edit:hover {
    background: #dcb535;
    color: white;
    transform: scale(1.05);
}

.panier-table .btn-delete i,
.panier-table .btn-edit i {
    font-size: 1.2rem;
    line-height: 1;
    display: block;
}

/* Style pour les prix */
.panier-table .price {
    font-weight: 600;
    color: #202020;
}

/* Style pour les quantités */
.panier-table .quantity {
    font-weight: 500;
    color: #666;
}

/* Style pour le message panier vide */
.panier-empty {
    text-align: center;
    padding: 30px;
    color: #666;
    font-style: italic;
}

/* Style pour le total du panier */
.panier-total {
    text-align: right;
    padding: 15px;
    font-weight: 600;
    font-size: 1.1rem;
    color: #202020;
    border-top: 2px solid #f2f2f2;
}

/* Style pour agrandir la modal */
.modal-dialog {
    max-width: 50% !important; 
    margin: 1.75rem auto;
}

@media (max-width: 768px) {
    .modal-dialog {
        max-width: 95% !important; 
        margin: 1rem auto;
    }
}

/* Ajuster la hauteur si nécessaire */
.modal-content {
    min-height: 80vh; 
}

/* Ajuster le corps de la modal pour le scroll */
.modal-body {
    max-height: calc(80vh - 120px); 
    overflow-y: auto;
}

/* Animation plus fluide */
.modal.fade .modal-dialog {
    transition: transform .3s ease-out;
}
