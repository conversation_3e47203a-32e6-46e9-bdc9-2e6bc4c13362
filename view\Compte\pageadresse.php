<!DOCTYPE html>
<html lang="fr-FR">
    <head>
        <title>CSGOSHOP</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <!-- inclus bootstrap via CDN = Content Delivery Network (réseau de distribution de contenu) -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <!-- inclus style.css -->
        <link href="<?php ROOT?>/view/CSS/style4.css" rel="stylesheet">
        <link href="<?php ROOT?>/view/CSS/panier.css" rel="stylesheet">

    </head>
    <body class="custom-body">
        <!-- Wrapper principal -->
        <div class="page-wrapper">
            <!-- Navbar -->
            <?php include_once ROOT.'/view/template/navbar.php'; ?>


            <!-- Titre -->
            <div class="container">
                <h1 class="mt-3">Espace Adresse</h1>

                <div class="col col-lg-3 mt-2">
                        <a href="/MonProfil/" class="text-dark underline-change"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-left" viewBox="0 0 16 18">
                        <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8"/>
                        </svg> Retour</a>
                </div>
            </div>

            <!-- paramètre Adresse -->
            <div class="container">
                <form action="/MonProfil/PageAdresse/" method="POST">
                    <div class="row">
                            <div class="col col-lg-3">
                            </div>
                        
                            <div class="col shadow-sm border rounded-3 bg-light">
                                <h1 class="text-center mt-3">Adresse</h1>

                                <?php echo $params['html']; ?> <!-- Affiche le succès de la modification -->

                                <div class="mt-5 ps-5 pe-5">
                                    <label for="adresse" class="mb-2"><strong>Adresse Postal :</strong></label>
                                    <input type="text" class="form-control" id="adresse" name="adresse" placeholder="Adresse Postale" value="<?php echo $params['adresse']; ?>"> <!-- Affiche l'adresse contenu dans la base de donnée de l'utilisateur -->
                                </div>

                                <div class="mt-2 ps-5 pe-5">
                                    <label for="pays" class="mb-2"><strong>Pays :</strong></label>
                                    <input type="text" class="form-control" id="pays" name="pays" placeholder="Pays" value="<?php echo $params['pays']; ?>"> <!-- Affiche le pays contenu dans la base de donnée de l'utilisateur -->
                                </div>

                                <div class="mt-2 ps-5 pe-5">
                                    <label for="cp" class="mb-2"><strong>Code Postal :</strong></label>
                                    <input type="text" class="form-control" id="cp" name="cp" placeholder="Code Postal" value="<?php echo $params['cp']; ?>"> <!-- Affiche le pays contenu dans la base de donnée de l'utilisateur -->
                                </div>

                                <div class="mt-2 ps-5 pe-5">
                                    <label for="ville" class="mb-2"><strong>Ville :</strong></label>
                                    <input type="text" class="form-control" id="ville" name="ville" placeholder="Ville" value="<?php echo $params['ville']; ?>"> <!-- Affiche la ville contenu dans la base de donnée de l'utilisateur -->
                                </div>

                                <div class="text-center mt-5 mb-3">
                                    <input class="btn btn-outline-dark" type="submit" id="connexion" value="Modifier">
                                </div>
                            </div>

                            <div class="col col-lg-3">
                            </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Footer -->
        <?php include_once ROOT.'/view/template/footer.php'; ?>

        <!-- Modal -->
        <?php include_once ROOT.'/view/template/panier.php'; ?>

        <!-- Chargement javascript -->
        <!-- js pour bootstrap -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script>

    </body>
</html>
