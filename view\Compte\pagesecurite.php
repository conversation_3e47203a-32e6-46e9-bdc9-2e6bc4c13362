<!DOCTYPE html>
<html lang="fr-FR">
    <head>
        <title>CSGOSHOP</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <!-- inclus bootstrap via CDN = Content Delivery Network (réseau de distribution de contenu) -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <!-- inclus style.css -->
        <link href="<?php ROOT?>/view/CSS/style4.css" rel="stylesheet">
        <link href="<?php ROOT?>/view/CSS/panier.css" rel="stylesheet">

    </head>
    <body class="custom-body">
        <!-- Wrapper principal -->
        <div class="page-wrapper">
            <!-- Navbar -->
            <?php include_once ROOT.'/view/template/navbar.php'; ?>


            <!-- Titre -->
            <div class="container">
                <h1 class="mt-3">Espace Connexion et Sécurité.</h1>

                <div class="col col-lg-3 mt-2">
                    <a href="/MonProfil/" class="text-dark underline-change"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-left" viewBox="0 0 16 18">
                    <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8"/>
                    </svg> Retour</a>
                </div>
            </div>

            <!-- paramètre connexion & Sécurité -->
            <div class="container">
                <form action="/MonProfil/PageSecurite/" method="POST">
                    <div class="row">
                            <div class="col col-lg-3">
                            </div>
                        
                            <div class="col shadow-sm border rounded-3 bg-light">
                                <h1 class="text-center mt-3">Information de Connexion</h1>

                                <?php echo $params['html']; ?> <!-- Affiche le succès de la modification -->

                                <div class="mt-2 ps-5 pe-5">
                                    <label for="pays" class="mb-2"><strong>Utilisateur :</strong></label>
                                    <input type="text" class="form-control" id="User" name="User" placeholder="Nom d'utilisateur" value="<?php echo $params['username']; ?>"> <!-- Affiche le nom d'utilisateur contenu dans la base de donnée de l'utilisateur -->
                                </div>

                                <div class="mt-2 ps-5 pe-5">
                                    <label for="adresse" class="mb-2"><strong>Nom :</strong></label>
                                    <input type="text" class="form-control" id="Nom" name="Nom" placeholder="Nom" value="<?php echo $params['nom']; ?>"> <!-- Affiche le nom contenu dans la base de donnée de l'utilisateur -->
                                </div>

                                <div class="mt-2 ps-5 pe-5">
                                    <label for="ville" class="mb-2"><strong>Prénom :</strong></label>
                                    <input type="text" class="form-control" id="Prenom" name="Prenom" placeholder="Prénom" value="<?php echo $params['prenom']; ?>"> <!-- Affiche le Prénom contenu dans la base de donnée de l'utilisateur -->
                                </div>

                                <div class="mt-2 ps-5 pe-5">
                                    <label for="pays" class="mb-2"><strong>E-mail :</strong></label>
                                    <input type="text" class="form-control" id="email" name="email" placeholder="email" value="<?php echo $params['email']; ?>"> <!-- Affiche l'email contenu dans la base de donnée de l'utilisateur -->
                                </div>

                                <div class="mt-2 ps-5 pe-5">
                                    <label for="pays" class="mb-2"><strong>Mobile :</strong></label>
                                    <input type="text" class="form-control" id="mobile" name="mobile" placeholder="Tel" value="<?php echo $params['mobile']; ?>"> <!-- Affiche le numéro de tel contenu dans la base de donnée de l'utilisateur -->
                                </div>

                                <div class="mt-2 ps-5 pe-5">
                                    <label for="pays" class="mb-2"><strong>Mot de passe :</strong></label>
                                    <input type="password" class="form-control" placeholder="Mot de passe" value="<?php echo $params['password']; ?>" disabled> <!-- Affiche le mot de passe contenu dans la base de donnée de l'utilisateur -->
                                    <input type="button" class="btn btn-outline-dark mt-2" value="Modifier mot de passe" data-bs-toggle="modal" data-bs-target="#Modal">
                                </div>

                                <div class="text-center mt-5 mb-3">
                                    <input class="btn btn-outline-dark" type="submit" id="connexion" value="Modifier">
                                </div>
                            </div>

                            <div class="col col-lg-3">
                            </div>
                    </div>
                </form>
            </div>

            <!-- MODAL PASSWORD -->
            <div class="modal" id="Modal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Modifier le mot de passe.</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <?php

                            $htmlpass ='';

                            $htmlform = '<form action="#" method="POST">
                            <label for="mdp" class="mb-2"><strong>Mot de passe actuel : </strong></label>
                            <input type="password" class="form-control" id="mdp" name="mdp" placeholder="Mot de passe actuel">
                            <input type="submit" class="btn btn-outline-dark mt-2" value="Entrez">
                            </form>';


                            if(isset($_SESSION['user'])){
                                if(isset($_POST['mdp'])){
                                    $inputmdp = htmlspecialchars($_POST['mdp']);

                                    if(DBManagerClient::getClient_Pwd($_SESSION['user'], $inputmdp) == false){
                                        $htmlpass .= '<p style="color: red;"> Mot de passe incorrect* </p>';
                                    }
                                    else{
                                        $htmlpass .= '';
                                        $htmlform = '';
                                        echo '<form name="form_mdp" action="#" method="POST">
                                        <label for="mdp2" class="mb-2"><strong>Entrez votre nouveau mot de passe: </strong></label>
                                        <input type="password" class="form-control" id="mdp2" name="mdp2" placeholder="Nouveau mot de passe">

                                        <label for="mdpconfirmation" class="mb-2"><strong>Confirmez votre nouveau mot de passe: </strong></label>
                                        <input type="password" class="form-control" id="mdpconfirmation" name="mdpconfirmation" placeholder="Nouveau mot de passe">
                                        <input type="button" class="btn btn-outline-dark mt-2" value="Entrez" onclick="submit_form()">
                                        </form>';
                                    }
                                }
                            }
                            echo $htmlpass; // Affiche l'erreur si le mot de passe est incorrect.
                            echo $htmlform;
                            ?> 
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <?php include_once ROOT.'/view/template/footer.php'; ?>

            <!-- Modal -->
            <?php include_once ROOT.'/view/template/panier.php'; ?>

            <!-- Chargement javascript -->
            <!-- js pour bootstrap -->
            <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script>
            <!-- js text file -->
            <script src="<?php ROOT?>/Js/formMdp.js"></script>
            <script src="<?php ROOT?>/Js/ModifQtePanier.js"></script>
        </div>
    </body>
</html>
