/* Style moderne pour les cards du backoffice */
.card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 20px;
    transition: all 0.3s ease;
    height: 200px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
    background: linear-gradient(135deg, #fff 0%, #f5f5f5 100%);
}

.card .fs-3, 
.card .fs-4 {
    color: #2d3436;
    font-weight: 600;
    margin-bottom: 15px;
    transition: color 0.3s ease;
}

.card:hover .fs-3,
.card:hover .fs-4 {
    color: #0984e3;
}

/* Style pour le rond avec l'icône */
.round {
    background: linear-gradient(145deg, #0984e3, #74b9ff);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(9, 132, 227, 0.3);
}

.round svg {
    color: white;
    width: 30px;
    height: 30px;
}

.card:hover .round {
    transform: rotate(360deg);
    background: linear-gradient(145deg, #0984e3, #6c5ce7);
}

/* Description text */
.card p:not(.round) {
    color: #636e72;
    font-size: 0.95rem;
    line-height: 1.4;
    transition: color 0.3s ease;
}

.card:hover p:not(.round) {
    color: #2d3436;
}

/* Layout adjustments */
.container {
    padding: 30px;
}

.row {
    gap: 20px;
}

/* Background pour le body */
.custom-body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

/* Titre de bienvenue */
.container h1 {
    color: #2d3436;
    font-size: 2.2rem;
    margin-bottom: 2rem;
    font-weight: 600;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    display: inline-block;
}

.container h1::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #0984e3, #acd6ff, #0984e3);
    background-size: 200% 100%;
    border-radius: 2px;
    animation: underlineAnimate 2s linear infinite;
}

@keyframes underlineAnimate {
    0% {
        background-position: 100% 0;
    }
    100% {
        background-position: -100% 0;
    }
}









