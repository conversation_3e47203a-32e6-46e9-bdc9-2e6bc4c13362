-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Hôte : 127.0.0.1:3307
-- <PERSON><PERSON><PERSON><PERSON> le : lun. 26 août 2024 à 14:46
-- Version du serveur : 11.2.2-MariaDB
-- Version de PHP : 8.2.13

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de données : `db_e-commerce`
--

-- --------------------------------------------------------

--
-- Structure de la table `admin`
--

DROP TABLE IF EXISTS `admin`;
CREATE TABLE IF NOT EXISTS `admin` (
  `idAdmin` int(11) NOT NULL AUTO_INCREMENT,
  `Prenom` varchar(60) NOT NULL,
  `Nom` varchar(60) NOT NULL,
  `Utilisateur` varchar(60) NOT NULL,
  `Password` varchar(50) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `Mobile` int(11) DEFAULT NULL,
  PRIMARY KEY (`idAdmin`)
) ENGINE=MyISAM AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `admin`
--

INSERT INTO `admin` (`idAdmin`, `Prenom`, `Nom`, `Utilisateur`, `Password`, `email`, `Mobile`) VALUES
(1, 'Alexis', 'P', 'Admin1', '@adminp', '<EMAIL>', 652413586),
(2, 'DIMITRI', 'PASYET', 'Admin2', '@adminpasyet', '<EMAIL>', 685426789);

-- --------------------------------------------------------

--
-- Structure de la table `categorie`
--

DROP TABLE IF EXISTS `categorie`;
CREATE TABLE IF NOT EXISTS `categorie` (
  `code_categ` smallint(6) NOT NULL AUTO_INCREMENT,
  `nom_categ` varchar(120) NOT NULL,
  PRIMARY KEY (`code_categ`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `categorie`
--

INSERT INTO `categorie` (`code_categ`, `nom_categ`) VALUES
(3, 'Karambite'),
(6, 'Bayonet'),
(7, 'Gut'),
(8, 'Huntsman'),
(9, 'Stiletto');

-- --------------------------------------------------------

--
-- Structure de la table `client`
--

DROP TABLE IF EXISTS `client`;
CREATE TABLE IF NOT EXISTS `client` (
  `idClient` smallint(6) NOT NULL AUTO_INCREMENT,
  `Nom` varchar(60) NOT NULL,
  `Prénom` varchar(60) NOT NULL,
  `Password` varchar(60) NOT NULL,
  `mobile` varchar(10) DEFAULT NULL,
  `email` varchar(120) NOT NULL,
  `Utilisateur` varchar(120) NOT NULL,
  `Adresse_Postal` varchar(240) DEFAULT NULL,
  `code_postal` char(5) DEFAULT NULL,
  `Ville` varchar(120) DEFAULT NULL,
  `Pays` varchar(60) DEFAULT NULL,
  PRIMARY KEY (`idClient`)
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `client`
--

INSERT INTO `client` (`idClient`, `Nom`, `Prénom`, `Password`, `mobile`, `email`, `Utilisateur`, `Adresse_Postal`, `code_postal`, `Ville`, `Pays`) VALUES
(17, 'POLLET', 'Alexis', '12345677', '0641327276', '<EMAIL>', 'User', '8 rue de machin', '', 'Montpellier', ''),
(18, 'Pollet', 'Alexis', '123', '0641327276', '<EMAIL>', 'Alexis Pollet', NULL, NULL, NULL, NULL),
(19, 'MOUAAAAAA', 'ANTOINE', 'ANTOINETTE', '0606070708', '<EMAIL>', 'ANTONIEDU93', NULL, NULL, NULL, NULL),
(20, 'Pollet', 'Alexis', '@pollet', '0641327276', '<EMAIL>', 'EDOUARD', '8 Rue beaux de maguielle', '30270', 'Saint jean du gard', 'France'),
(21, 'Plt', 'Valentin', 'Juju010602!', '0631444378', '<EMAIL>', 'Crème Fouettée', '69 Avenue pas de moi', NULL, 'Nullepart', 'France'),
(22, 'Gaudin', 'Valérie', 'Benicovalalex@!', '0632564323', '<EMAIL>', 'VALOU5', '8 rue beaux de maguielle', NULL, 'Saint jean du gard', 'France'),
(35, 'Administrateur', 'CSGOSHOP', '@@@Administrateur', '0641327276', '<EMAIL>', 'Admin', NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Structure de la table `commandes`
--

DROP TABLE IF EXISTS `commandes`;
CREATE TABLE IF NOT EXISTS `commandes` (
  `refCmd` smallint(6) NOT NULL AUTO_INCREMENT,
  `DateCmd` date NOT NULL,
  `DateLiv` date DEFAULT NULL,
  `mode_paiement` varchar(60) NOT NULL,
  `idClient` smallint(6) NOT NULL,
  PRIMARY KEY (`refCmd`),
  KEY `fk_commandes_idClient` (`idClient`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Structure de la table `details_cms`
--

DROP TABLE IF EXISTS `details_cms`;
CREATE TABLE IF NOT EXISTS `details_cms` (
  `refCmd` smallint(6) NOT NULL,
  `idProduit` smallint(6) NOT NULL,
  `Qte` int(11) NOT NULL,
  PRIMARY KEY (`refCmd`,`idProduit`),
  KEY `fk_produit_idProduit` (`idProduit`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Structure de la table `panier`
--

DROP TABLE IF EXISTS `panier`;
CREATE TABLE IF NOT EXISTS `panier` (
  `idClient` smallint(6) NOT NULL,
  `idProduit` smallint(6) NOT NULL,
  `qte` int(11) NOT NULL,
  PRIMARY KEY (`idClient`,`idProduit`),
  KEY `fk_panier_idProduit` (`idProduit`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Structure de la table `produit`
--

DROP TABLE IF EXISTS `produit`;
CREATE TABLE IF NOT EXISTS `produit` (
  `IdProduit` smallint(6) NOT NULL AUTO_INCREMENT,
  `Nom` varchar(60) NOT NULL,
  `Quantité` int(11) NOT NULL,
  `PrixUnitaire` float NOT NULL,
  `code_categ` smallint(6) NOT NULL,
  `image_url` varchar(255) DEFAULT NULL,
  `Vente_Flash` smallint(1) DEFAULT NULL,
  `Stock` int(11) DEFAULT NULL,
  `descp` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`IdProduit`),
  KEY `fk_produit_categ` (`code_categ`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `produit`
--

INSERT INTO `produit` (`IdProduit`, `Nom`, `Quantité`, `PrixUnitaire`, `code_categ`, `image_url`, `Vente_Flash`, `Stock`, `descp`) VALUES
(3, 'Karambite Lore', 1, 79.99, 3, '/Images/K4.jpeg', NULL, 15, 'Le couteau Karambite Lore de CS:GO, arborant un design intrigant inspiré du folklore, combine élégance artistique et efficacité tactique pour les passionnés de jeux vidéo.'),
(4, 'Bayonnet Autotronic', 1, 43.99, 6, '/Images/A1.jpeg', NULL, 30, 'Découvrez le couteau bayonnet Autotronic de CS:GO, une lame emblématique au design futuriste et à l''éclat technologique, ajoutant une touche de sophistication à votre arsenal virtuel.'),
(5, 'Karambite Fade', 1, 65, 3, '/Images/K1.jpg', NULL, 40, 'Explorez l''esthétique hypnotique du couteau Karambite Fade de CS:GO, fusion parfaite de couleurs vives et de lignes épurées dans l''univers du jeu vidéo emblématique.'),
(6, 'Gut Fade', 1, 27.99, 7, '/Images/G1.jpg', 1, 55, 'Le couteau Gut Fade de CS:GO séduit par son design flamboyant, Mêlant des nuances vives et un motif hypnotique, faisant de lui un choix de prestige pour les amateurs de skins rares dans Counter-Strike.'),
(7, 'Bayonet M9 Night', 1, 55.99, 6, '/Images/M1.jpeg', NULL, 13, 'Le couteau bayonnet M9 Night allie élégance et fonctionnalité avec son design distinctif et sa lame tranchante, offrant une expérience tactile et visuelle exceptionnelle.'),
(8, 'Karambite Night', 1, 69.99, 3, '/Images/K3.jpeg', NULL, 21, 'Le couteau Karambite Night allie élégance et fonctionnalité avec son design épuré et sa lame tranchante, offrant une expérience de coupe précise et fiable dans l''obscurité.'),
(9, 'Huntsman Tiger', 1, 34.99, 8, '/Images/H3.jpeg', NULL, 98, 'Découvrez l''élégance et la précision du couteau Huntsman Tiger, un compagnon indispensable qui allie design raffiné et performance exceptionnelle.'),
(10, 'Bayonnet Emerald', 1, 49.99, 6, '/Images/B1.jpg', NULL, 58, 'Le couteau Bayonnet Emeralds allie élégance et fonctionnalité avec son design sophistiqué et ses incrustations d''émeraudes, offrant une expérience unique pour les amateurs de coutellerie.'),
(11, 'Stiletto Fade', 1, 19.24, 9, '/Images/S1.jpeg', 1, 8, 'Découvrez le couteau Stiletto Fade, une fusion audacieuse de style et de fonctionnalité, alliant un design élégant à une performance fiable.'),
(12, 'Huntsman Dopplet Phase 2', 1, 69.99, 8, '/Images/H1.jpg', NULL, 69, 'Découvrez le couteau Huntsman Dopplet Phase 2 : un mélange saisissant de style et de fonctionnalité pour les aventuriers modernes.'),
(13, 'Karambite Blade', 1, 44.99, 3, '/Images/K2.jpeg', 1, 0, 'Le Karambite Blade : un couteau au design distinctif, avec une lame incurvée et une poignée ergonomique, idéal pour une variété d''applications tactiques.'),
(14, 'Huntsman Fade', 1, 44.99, 8, '/Images/H2.jpeg', NULL, 26, 'Le Huntsman Fade est une fusion audacieuse de style et de fonctionnalité, alliant un design élégant à une performance fiable. Un couteau mélangeant les couleurs le rend plus sublime.');

--
-- Contraintes pour les tables déchargées
--

--
-- Contraintes pour la table `commandes`
--
ALTER TABLE `commandes`
  ADD CONSTRAINT `fk_commandes_idClient` FOREIGN KEY (`idClient`) REFERENCES `client` (`idClient`);

--
-- Contraintes pour la table `details_cms`
--
ALTER TABLE `details_cms`
  ADD CONSTRAINT `fk_details_cms_refCmd` FOREIGN KEY (`refCmd`) REFERENCES `commandes` (`refCmd`),
  ADD CONSTRAINT `fk_produit_idProduit` FOREIGN KEY (`idProduit`) REFERENCES `produit` (`IdProduit`);

--
-- Contraintes pour la table `panier`
--
ALTER TABLE `panier`
  ADD CONSTRAINT `fk_panier_idClient` FOREIGN KEY (`idClient`) REFERENCES `client` (`idClient`),
  ADD CONSTRAINT `fk_panier_idProduit` FOREIGN KEY (`idProduit`) REFERENCES `produit` (`IdProduit`);

--
-- Contraintes pour la table `produit`
--
ALTER TABLE `produit`
  ADD CONSTRAINT `fk_produit_categ` FOREIGN KEY (`code_categ`) REFERENCES `categorie` (`code_categ`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
