<?php
// Définition de la classe DetailsCmd

class DetailsCmd{

    private int $RefCmd;
    private int $idProduit;
    private int $qte;

    public function __construct($RefCmd, $idProduit, $qte){
        $this->RefCmd = $RefCmd;
        $this->idProduit = $idProduit;
        $this->qte = $qte;
    }

    public function getRefCmd() { return $this->RefCmd; }

    public function getIdProduit() { return $this->idProduit; }

    public function getQte() { return $this->qte; }

    public function setQte($qte) { return $this->qte = $qte; }
}

?>