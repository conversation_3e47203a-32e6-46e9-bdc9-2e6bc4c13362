<?php
class PageAdresseController extends Controller{


    public static function index($params){

        // Vérifie si l'utilisateur est connecté en tant qu'user
        if (!isset($_SESSION['user'])) {
            // Redirige vers la page de connexion si non connecté
            header("Location: /Connexion/");
            exit();
        }

        $params['html'] = '';

        $params['adresse'] = DBManagerClient::getClientByName2($_SESSION['user'])->getAdresse_postale(); // Récupère la valeur de Adresse_Postal
        $params['ville'] = DBManagerClient::getClientByName2($_SESSION['user'])->getVille(); // Récupère la valeur de Ville
        $params['pays'] = DBManagerClient::getClientByName2($_SESSION['user'])->getPays(); // Récupère la valeur de Pays
        $params['cp'] = DBManagerClient::getClientByName2($_SESSION['user'])->getCode_postal(); // Récupère la valeur de code postal

        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $updated = false;
            //Vérifie si il y a bien un utilisateur
            if(isset($_SESSION['user'])){
                //vérifie l'adresse postal
                if (!empty($_POST['adresse']) && $_POST['adresse'] !== $params['adresse']) {
                    $adresse = htmlspecialchars($_POST['adresse']);
                    DBManagerClient::ModifAPClient($adresse, $_SESSION['user']);
                    $updated = true;
                }
                //vérifie la ville
                if(!empty($_POST['ville']) && $_POST['ville'] !== $params['ville']){
                    $ville = htmlspecialchars($_POST['ville']);
                    DBManagerClient::ModifVilleClient($ville, $_SESSION['user']);
                    $updated = true;
                }
                // vérifie le pays
                if(isset($_POST['pays']) && $_POST['pays'] !== $params['pays']){
                    $pays = htmlspecialchars($_POST['pays']);
                    DBManagerClient::ModifPaysClient($pays, $_SESSION['user']);
                    $updated = true;
                }

                // vérifie le code postal
                if(isset($_POST['cp']) && $_POST['cp'] !== $params['cp']){
                    $cp = htmlspecialchars($_POST['cp']);
                    DBManagerClient::ModifCPClient($cp, $_SESSION['user']);
                    $updated = true;
                }

                if($updated){
                    $params['html'] = '<p class="fs-5 text-center" style="color: green;"> Modification effectué avec succès ! </p>';
                }
            }
        }
        
        // appelle la vue
        $view = ROOT."/view/Compte/pageadresse.php";
        self::render($view, $params);
    }

}
?>