<?php
// Définition de la classe Commande

class Commande{

    private int $RefCmd;
    private string $dateCmd;
    private ?string $dateLiv;
    private int $mode_paiement;
    private int $idClient;

    public function __construct($RefCmd, $dateCmd, $dateLiv, $mode_paiement, $idClient){
        $this->RefCmd = $RefCmd;
        $this->dateCmd = $dateCmd;
        $this->dateLiv = $dateLiv;
        $this->mode_paiement = $mode_paiement;
        $this->idClient = $idClient;
    }

    public function getRefCmd() { return $this->RefCmd; }

    public function getDateCmd() { return $this->dateCmd; }

    public function getDateLiv() { return $this->dateLiv; }

    public function getModePaiement() { return $this->mode_paiement; }

    public function getIdClient() { return $this->idClient; }

    public function setDateCmd($dateCmd) { return $this->dateCmd = $dateCmd; }

    public function setDateLiv($dateLiv) { return $this->dateLiv = $dateLiv; }

    public function setModePaiement($mode_paiement) { return $this->mode_paiement = $mode_paiement; }
}

?>