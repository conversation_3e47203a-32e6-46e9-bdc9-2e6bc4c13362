<!DOCTYPE html>
<html lang="fr-FR">
    <head>
        <title>CSGOSHOP</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <!-- inclus bootstrap via CDN = Content Delivery Network (réseau de distribution de contenu) -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <!-- inclus style.css -->
        <link href="<?php ROOT?>/view/CSS/style.css" rel="stylesheet">

    </head>
    <body class="custom-body">

        <!-- bouton retour en haut de la page -->
        <div class="container mt-3">
            <a href="/BO-Admin/Accueil/" class="btn-return">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-left" viewBox="0 0 16 16">
                    <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8"/>
                </svg>
                <span class="fw-semibold">Retour à l'accueil</span>
            </a>
        </div>

        <!-- Visualisation des commandes -->
        <div class="container">
        <div class="row">
                <div class="col mt-4">
                    <h1>Commandes actuelles</h1>
                    <!-- Tableau des Commandes -->
                    <table class="table table-striped tablep mt-4">
                        <thead>
                            <th>Reférence de la commande</th>
                            <th>Date de la commande</th>
                            <th>Date de Livraison</th>
                            <th>Mode de paiement</th>
                            <th>Client</th>
                        </thead>
                        <tbody>
                            <?php
                                $html = '';
                                foreach (DBManagerCommande::getLesCommandes() as $commande) {
                                    $html .= '<tr>';
                                    $html .= '<td>' . $commande->getRefCmd() . '</td>';
                                    $html .= '<td>' . $commande->getDateCmd() . '</td>';
                                    $html .= '<td>' . $commande->getDateLiv() . '</td>';
                                    $html .= '<td>' . $commande->getModePaiement() . '</td>';
                                    $html .= '<td>' . DBManagerClient::getClientByID($commande->getIdClient())->getUtilisateur() . '</td>';
                                    $html .= '</tr>';
                                }
                                echo $html;
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>



        <!-- Chargement javascript -->
        <!-- js pour bootstrap -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script>
        <!-- js text file -->
        <script src="<?php ROOT?>/Js/backOffice.js"></script>

    </body>
</html>
