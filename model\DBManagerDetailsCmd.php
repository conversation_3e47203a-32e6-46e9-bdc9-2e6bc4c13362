<?php
class DBManagerDetailsCmd {
    private static ?\PDO $cnx;
    private static DetailsCmd $detailsCmd;
    private static array $LesDetailsCmd;

    public function __construct(){
        self::$detailsCmd = new DetailsCmd(0, 0, 0);
    }

    public static function getLesDetailsCmd() {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Initialiser un tableau pour stocker les DetailsCmd
        self::$LesDetailsCmd = [];

        // Requête SQL
        $query = "SELECT RefCmd, IdProduit, qte FROM details_cms";

        // Préparer la requête
        $sql = self::$cnx->prepare($query);

        // Exécuter la requête
        $sql->execute();

        // Récupérer les DetailsCmd
        while ($row = $sql->fetch(PDO::FETCH_ASSOC)) {
            self::$detailsCmd = new DetailsCmd($row['RefCmd'], $row['IdProduit'], $row['qte']);
            self::$LesDetailsCmd[] = self::$detailsCmd;
        }

        return self::$LesDetailsCmd;
    }

    public static function InsertDetailsCmd($RefCmd, $idProduit, $qte) {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "INSERT INTO details_cms(RefCmd, IdProduit, qte) VALUES(?, ?, ?)";

        // Préparer la requête
        $sql = self::$cnx->prepare($query);

        // Exécuter la requête
        return $sql->execute([$RefCmd, $idProduit, $qte]);
    }

    /**
     * Récupère les détails de la commande par l'id de la commande
     */
    public static function getDetailsCmdByIdCmd($id) {        
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Initialiser un tableau
        self::$LesDetailsCmd = [];

        // Requête SQL
        $query = "SELECT RefCmd, IdProduit, qte FROM details_cms WHERE RefCmd = ?";

        // Préparer la requête
        $sql = self::$cnx->prepare($query);

        // Exécuter la requête
        $sql->execute([$id]);

        // Récupérer les DetailsCmd
        while ($row = $sql->fetch(PDO::FETCH_ASSOC)) {
            self::$detailsCmd = new DetailsCmd($row['RefCmd'], $row['IdProduit'], $row['qte']);
            self::$LesDetailsCmd[] = self::$detailsCmd;
        }

        return self::$LesDetailsCmd;
    }


}