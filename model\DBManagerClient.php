<?php
class DBManagerClient {
    private static ?\PDO $cnx;
    private static Client $client;
    private static array $LesClients;

    public function __construct(){
        self::$client = new Client("", "", "", "", "", "", "", "", "", "", "");
    }

    /**
     * Permet de récupérer un client dans une base de donnée de par son nom d'utilisateur. Et retourne si ça existe ou non
     * 
     */
    public static function getClientByName($ID) {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "SELECT idClient, Nom, Prénom, Password, mobile, email, Utilisateur, Adresse_postal, code_postal, Ville, Pays FROM Client Where Utilisateur = ?";

        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);

        // Exécuter la requête
        $sql1->execute([$ID]);

        $resultId = $sql1->fetchColumn();

        return $resultId;
    }

    /**
     * Permet de récupérer le mot de passe d'un client dans une base de donnée. Et retourne si ça existe ou non
     * 
     */
    public static function getClient_Pwd($ID, $Pwd)
    {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "SELECT Password FROM Client Where Password = ? AND Utilisateur = ?";
        
        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);

        // Exécuter la requête
        $sql1->execute([$Pwd, $ID]);

        $resultpass = $sql1->fetchColumn();

        return $resultpass;
    }



    /**
     * Permet de récupérer un client dans une base de donnée
     * 
     */
    public static function getClientByID($ID) {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "SELECT idClient, Nom, Prénom, Password, mobile, email, Utilisateur, Adresse_postal, code_postal, Ville, Pays ";
        $query .= "FROM Client Where idClient = ?";

        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);

        // Exécuter la requête
        $sql1->execute([$ID]);

        // Récupère la valeur du client
        $row = $sql1->fetch();

        if ($row) {
            self::$client = new Client($row['idClient'], $row['Utilisateur'], $row['Nom'], $row['Prénom'], $row['Password'], $row['mobile'], $row['email'], $row['Adresse_postal'], $row['code_postal'], $row['Ville'], $row['Pays']);
        }

        return self::$client;
    }

    /**
     * Permet de récupérer un client de par son username dans une base de donnée. Et retourne le client
     * 
     */
    public static function getClientByName2($ID) {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();
        
        // Requête SQL
        $query = "SELECT idClient, Nom, Prénom, Password, mobile, email, Utilisateur, Adresse_postal, code_postal, Ville, Pays ";
        $query .= "FROM Client Where Utilisateur = ?";

        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);

        // Exécuter la requête
        $sql1->execute([$ID]);

        // Récupère la valeur du client
        $row = $sql1->fetch();

        if ($row) {
            self::$client = new Client($row['idClient'], $row['Utilisateur'], $row['Nom'], $row['Prénom'], $row['Password'], $row['mobile'], $row['email'], $row['Adresse_postal'], $row['code_postal'], $row['Ville'], $row['Pays']);
        }

        return self::$client;
    }


    /**
     * Permet de récupérer tous les clients de la base de données.
     * 
     */
    public static function getClients() {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        //Initialise le tableau des clients
        self::$LesClients = [];

        // Requête SQL
        $query = "SELECT idClient, Nom, Prénom, Password, mobile, email, Utilisateur, Adresse_postal, code_postal, Ville, Pays ";
        $query .= "FROM Client";

        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);

        // Exécuter la requête
        $sql1->execute();

        while($row = $sql1->fetch(PDO::FETCH_ASSOC)) {
            $clients = new Client($row['idClient'], $row['Utilisateur'], $row['Nom'], $row['Prénom'], $row['Password'], $row['mobile'], $row['email'], $row['Adresse_postal'], $row['code_postal'], $row['Ville'], $row['Pays']);
            self::$LesClients[] = $clients;
        }

        return self::$LesClients;
    }


    /**
     * Modifie le nom d'utilisateur de l'utilisateur
     * return bool
     */
    public static function ModifUserNameClient($user, $user2){
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "UPDATE Client SET Utilisateur = ? where Utilisateur = ?";
        
        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);
        
        // Exécuter la requête
        $nameuser = $sql1->execute([$user, $user2]);

        return $nameuser;
    }

    /**
     * Modifie le prénom de l'utilisateur
     * return bool
     */
    public static function ModifFirstNameClient($prenom ,$user2){
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "UPDATE Client SET Prénom = ? where Utilisateur = ?";
        
        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);
        
        // Exécuter la requête
        $firstname = $sql1->execute([$prenom, $user2]);

        return $firstname;
    }

    /**
     * Modifie le nom de l'utilisateur
     * return bool
     */
    public static function ModifNameClient($nom ,$user2){
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "UPDATE Client SET Nom = ? where Utilisateur = ?";
        
        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);
        
        // Exécuter la requête
        $name = $sql1->execute([$nom, $user2]);

        return $name;
    }

    /**
     * Modifie l'email de l'utilisateur
     * return bool
     */
    public static function ModifEmailClient($email, $user2){
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "UPDATE Client SET email = ? where Utilisateur = ?";
        
        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);
        
        // Exécuter la requête
        $email = $sql1->execute([$email, $user2]);

        return $email;
    }

    
    /**
     * Modifie le mobile de l'utilisateur
     * return bool
     */
    public static function ModifMobileClient($mobile ,$user2){
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "UPDATE Client SET mobile = ? where Utilisateur = ?";
        
        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);
        
        // Exécuter la requête
        $mobile = $sql1->execute([$mobile, $user2]);

        return $mobile;
    }


    /**
     * Modifie le mot de passe de l'utilisateur
     * return bool
     */
    public static function ModifPasswordClient($mdp ,$user2){
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "UPDATE Client SET Password = ? where Utilisateur = ?";
        
        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);
        
        // Exécuter la requête
        $pass = $sql1->execute([$mdp, $user2]);

        return $pass;
    }


    /**
     * Modifie l'adresse postale de l'utilisateur
     * return bool
     */
    public static function ModifAPClient($adresse ,$user2){
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "UPDATE Client SET Adresse_Postal = ? where Utilisateur = ?";
        
        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);
        
        // Exécuter la requête
        $ad = $sql1->execute([$adresse, $user2]);

        return $ad;
    }


    /**
     * Modifie la ville de l'utilisateur
     * return bool
     */
    public static function ModifVilleClient($ville ,$user2){
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "UPDATE Client SET Ville = ? where Utilisateur = ?";
        
        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);
        
        // Exécuter la requête
        $v = $sql1->execute([$ville, $user2]);

        return $v;
    }

    /**
     * Modifie la Pays de l'utilisateur
     * return bool
     */
    public static function ModifPaysClient($pays ,$user2){
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "UPDATE Client SET Pays = ? where Utilisateur = ?";
        
        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);
        
        // Exécuter la requête
        $p = $sql1->execute([$pays, $user2]);

        return $p;
    }

    /**
     * Modifie la code postale de l'utilisateur
     * return bool
     */
    public static function ModifCPClient($codepostale ,$user2){
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();
        
        // Requête SQL
        $query = "UPDATE Client SET code_postal = ? where Utilisateur = ?";
        
        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);
        
        // Exécuter la requête
        $cp = $sql1->execute([$codepostale, $user2]);

        return $cp;
    }

    /**
     * Modifie la code postale de l'utilisateur
     * return bool
     */
    public static function InsertClient($nom, $prenom, $pwd, $tel, $mail, $Username){
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();
        
        // Requête SQL
        $query = "INSERT INTO client(Nom, Prénom, Password, mobile, email, Utilisateur) VALUES(?, ?, ?, ?, ?, ?)";
        
        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);
        
        // Exécuter la requête
        $insert = $sql1->execute([$nom, $prenom, $pwd, $tel, $mail, $Username]);

        return $insert;
    }
}


?>