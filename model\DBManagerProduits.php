<?php
class DBManagerProduits {
    private static ?\PDO $cnx;
    private static array $Produits = array();
    private static ?Produit $produit = null;  

    public function __construct(){
        self::$produit = new Produit(0,'', 0, 0, '', 0, '', 0, 0);
    }


    public static function getLesProduits() {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Initialiser un tableau pour stocker les produits
        self::$Produits = [];

        // Requête SQL
        $query = "SELECT IdProduit, Nom, PrixUnitaire, code_categ, image_url, Stock, descp ,Vente_Flash, StockAlert ";
        $query .= "FROM Produit";

        // Préparer la requête
        $sql = self::$cnx->prepare($query);

        // Exécuter la requête
        $sql->execute();

        // Récupérer les produits
        while ($row = $sql->fetch(PDO::FETCH_ASSOC)) {
            self::$produit = new Produit($row['IdProduit'] ,$row['Nom'], $row['PrixUnitaire'], $row['code_categ'], $row['image_url'], $row['Stock'], $row['descp'], $row['Vente_Flash'], $row['StockAlert']);
            self::$Produits[] = self::$produit;
        }

        return self::$Produits;
    }

    /**
     * Permet de récupérer un produit dans une base de donnée
     * 
     */
    public static function getProduitSelect($get) : ?Produit {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "SELECT IdProduit ,Nom, PrixUnitaire, code_categ, image_url, Stock, descp ,Vente_Flash, StockAlert FROM Produit WHERE Nom = ?";
        
        // Préparer la requête
        $sql = self::$cnx->prepare($query);
        
        // Exécuter la requête
        $sql->execute([$get]);

        // Récupérer le produit
        $row = $sql->fetch();
        
        if ($row) {
            self::$produit = new Produit($row['IdProduit'] ,$row['Nom'], $row['PrixUnitaire'], $row['code_categ'], $row['image_url'], $row['Stock'], $row['descp'], $row['Vente_Flash'], $row['StockAlert']);
        }
        return self::$produit;
    }

    /**
     * Permet de récupérer un produit dans une base de donnée de par un id
     * 
     */
    public static function getProduitByID($id) : ?Produit {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "SELECT IdProduit ,Nom, PrixUnitaire, code_categ, image_url, Stock, descp ,Vente_Flash, StockAlert FROM Produit WHERE IdProduit = ?";
        
        // Préparer la requête
        $sql = self::$cnx->prepare($query);
        
        // Exécuter la requête
        $sql->execute([$id]);

        // Récupérer le produit
        $row = $sql->fetch();
        
        if ($row) {
            self::$produit = new Produit($row['IdProduit'] ,$row['Nom'], $row['PrixUnitaire'], $row['code_categ'], $row['image_url'], $row['Stock'], $row['descp'], $row['Vente_Flash'], $row['StockAlert']);
        }
        return self::$produit;
    }


    /**
     * Permet de récupérer des produits de par un filtre
     * 
     */
    public static function getProduitfiltre(array $filtres) : array {
        // Réinitialiser le tableau de produits
        self::$Produits = [];
        
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "SELECT IdProduit, Nom, PrixUnitaire, code_categ, image_url, Stock, descp, Vente_Flash, StockAlert
                  FROM Produit";

        if(!in_array("Tous", $filtres)){
            $query .= " WHERE (0"; // Condition inutile pour par la suite rajouter d'autres conditions derrière

    
            if (in_array('Karambite', $filtres)) {
                if (in_array('VenteFlash', $filtres)) {
                    $query .= " OR (code_categ = 3 AND Vente_Flash = 1)";
                } else {
                    $query .= " OR code_categ = 3";
                }
            }
    
            if (in_array('Bayonnet', $filtres)) {
                if (in_array('VenteFlash', $filtres)) {
                    $query .= " OR (code_categ = 6 AND Vente_Flash = 1)";
                } else {
                    $query .= " OR code_categ = 6";
                }
            }
    
            if (in_array('Gut', $filtres)) {
                if (in_array('VenteFlash', $filtres)) {
                    $query .= " OR (code_categ = 7 AND Vente_Flash = 1)";
                } else {
                    $query .= " OR code_categ = 7";
                }
            }
    
            if (in_array('Huntsman', $filtres)) {
                if (in_array('VenteFlash', $filtres)) {
                    $query .= " OR (code_categ = 8 AND Vente_Flash = 1)";
                } else {
                    $query .= " OR code_categ = 8";
                }
            }
    
            if (in_array('Stiletto', $filtres)) {
                if (in_array('VenteFlash', $filtres)) {
                    $query .= " OR (code_categ = 9 AND Vente_Flash = 1)";
                } else {
                    $query .= " OR code_categ = 9";
                }
            }
    
            $query .= ")";  // Ferme la parenthèse de la condition
        }
        else if (in_array('VenteFlash', $filtres)) {
            $query .= " WHERE Vente_Flash = 1";
        }
    
        // Prépare la requête
        $sql = self::$cnx->prepare($query);
    
        // Exécute la requête
        $sql->execute();
    
        // Récupère les produits
        while ($row = $sql->fetch(PDO::FETCH_ASSOC)) {
            self::$produit = new Produit($row['IdProduit'] ,$row['Nom'], $row['PrixUnitaire'], $row['code_categ'], $row['image_url'], $row['Stock'], $row['descp'], $row['Vente_Flash'], $row['StockAlert']);
            self::$Produits[] = self::$produit;
        }
    
        return self::$Produits;
    }

    /**
     * Permet de récupérer des produits par une recherche
     * 
     */
    public static function getProduitsBySearch($recherche) : array {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "SELECT IdProduit ,Nom, PrixUnitaire, code_categ, image_url, Stock, descp ,Vente_Flash, StockAlert FROM Produit WHERE ";

        //trim supprime les espaces blanc // explode = transforme un tableau de chaine caractere
        $mots = explode(" ", trim($recherche)); 

        // Pour chaque mots on ajoute une requête dans un tableau
        $tabquery = [];
        foreach($mots as $mot){
            $tabquery[] = "nom like ?";
        }

        // Tranforme un tableau en chaine de caractere.
        $query .= implode(" OR ", $tabquery); 
    
        // Prépare la requête
        $sql = self::$cnx->prepare($query);

        // Pour chaque mot on ajoute un mot à notre tableau pour remplacer les ?
        $tabforexecute = [];
        foreach($mots as $mot){
            $tabforexecute[] = '%'. $mot .'%';
        }
    
        // Exécute la requête
        $sql->execute($tabforexecute);
    
        // Récupère les produits
        while ($row = $sql->fetch(PDO::FETCH_ASSOC)) {
            self::$produit = new Produit($row['IdProduit'] ,$row['Nom'], $row['PrixUnitaire'], $row['code_categ'], $row['image_url'], $row['Stock'], $row['descp'], $row['Vente_Flash'], $row['StockAlert']);
            self::$Produits[] = self::$produit;
        }
    
        return self::$Produits;
    }


    /**
     * Supprime les produits par une recherche
     * 
     */
    public static function DelProduitsByName($nomproduit) {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL pour supprimer le produit
        $query = "DELETE FROM Produit WHERE Nom = ?";
    
        // Prépare la requête
        $sql = self::$cnx->prepare($query);
    
        // Exécute la requête
        $sql->execute([$nomproduit]);
    
    }

    /**
     * Supprime les produits par l'ID
     * 
     */
    public static function DelProduitsByID($idproduit) {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL pour supprimer le produit
        $query = "DELETE FROM Produit WHERE idProduit = ?";
    
        // Prépare la requête
        $sql = self::$cnx->prepare($query);
    
        // Exécute la requête
        $sql->execute([$idproduit]);
    
    }

    /**
     * Modifie le stock du produit
     * return bool
     */
    public static function ModifStockProduit($stock ,$produit){
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "UPDATE Produit SET Stock = ? where Nom = ?";
        
        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);
        
        // Exécuter la requête
        $cp = $sql1->execute([$stock, $produit]);

        return $cp;
    }
    

    /**
     * Ajoute un produit
     * return bool
     */
    public static function AjouterProduit($Nom, $Prix, $code_categ, $image_url, $Stock, $descp, $VenteFlash, $StockAlert){
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();
    
        // Requête SQL
        $query = "INSERT INTO Produit(Nom, PrixUnitaire, code_categ, image_url, Stock, descp ,Vente_Flash, StockAlert) ";
        $query .= "VALUES(?, ?, ?, ?, ?, ?, ?, ?)";
        
        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);
        
        // Exécuter la requête
        $produit = $sql1->execute([$Nom, $Prix, $code_categ, $image_url, $Stock, $descp, $VenteFlash, $StockAlert]);

        return $produit;
    }

    /**
     * Fixtures des produits
     * return bool
     */
    public static function FixturesProduits(){
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Supprime les details de commande car il y a une contrainte d'intégrité référentiel
        $query = "DELETE FROM Details_cms";
        $sql1 = self::$cnx->prepare($query);
        $sql1->execute();

        // Supprime les produits
        $query = "DELETE FROM Produit";
        $sql1 = self::$cnx->prepare($query);
        $sql1->execute();
    
        // Requête SQL
        $query = "INSERT INTO Produit(IdProduit, Nom, PrixUnitaire, code_categ, image_url, Vente_Flash, Stock, descp, StockAlert) VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $tableaudesproduits = array(
            [1, 'Karambit Lore', 79.99, 3, '/Images/K4.jpeg', NULL, 15, 'Le couteau Karambit Lore de CS:GO, arborant un design intrigant inspiré du folklore, combine élégance artistique et efficacité tactique pour les passionnés de jeux vidéo.', 50],
            [2, 'Bayonnet Autotronic', 43.99, 6, '/Images/A1.jpeg', NULL, 30, 'Découvrez le couteau bayonnet Autotronic de CS:GO, une lame emblématique au design futuriste et à l\'éclat technologique, ajoutant une touche de sophistication à votre arsenal virtuel.', 80],
            [3, 'Karambit Fade', 65, 3, '/Images/K1.jpg', NULL, 40, 'Explorez l\'esthétique hypnotique du couteau Karambit Fade de CS:GO, fusion parfaite de couleurs vives et de lignes épurées dans l\'univers du jeu vidéo emblématique.', 89],
            [4, 'Gut Fade', 27.99, 7, '/Images/G1.jpg', 1, 55, 'Le couteau Gut Fade de CS:GO séduit par son design flamboyant, Mêlant des nuances vives et un motif hypnotique, faisant de lui un choix de prestige pour les amateurs de skins rares dans Counter-Strike.', 47],
            [5, 'Bayonet M9 Night', 55.99, 6, '/Images/M1.jpeg', NULL, 13, 'Le couteau bayonnet M9 Night allie élégance et fonctionnalité avec son design distinctif et sa lame tranchante, offrant une expérience tactile et visuelle exceptionnelle.', 75],
            [6, 'Karambit Night', 69.99, 3, '/Images/K3.jpeg', NULL, 21, 'Le couteau Karambit Night allie élégance et fonctionnalité avec son design épuré et sa lame tranchante, offrant une expérience de coupe précise et fiable dans l\'obscurité.', 54],
            [7, 'Huntsman Tiger', 34.99, 8, '/Images/H3.jpeg', NULL, 98, 'Découvrez l\'élégance et la précision du couteau Huntsman Tiger, un compagnon indispensable qui allie design raffiné et performance exceptionnelle.', 78],
            [8, 'Bayonnet Emerald', 49.99, 6, '/Images/B1.jpg', NULL, 58, 'Le couteau Bayonnet Emeralds allie élégance et fonctionnalité avec son design sophistiqué et ses incrustations d\'émeraudes, offrant une expérience unique pour les amateurs de coutellerie.', 67],
            [9, 'Stiletto Fade', 19.24, 9, '/Images/S1.jpeg', 1, 8, 'Découvrez le couteau Stiletto Fade, une fusion audacieuse de style et de fonctionnalité, alliant un design élégant à une performance fiable.', 78],
            [10, 'Huntsman Dopplet Phase 2', 69.99, 8, '/Images/H1.jpg', NULL, 69, 'Découvrez le couteau Huntsman Dopplet Phase 2 : un mélange saisissant de style et de fonctionnalité pour les aventuriers modernes.', 36],
            [11, 'Karambit Blade', 44.99, 3, '/Images/K2.jpeg', 1, 0, 'Le Karambit Blade : un couteau au design distinctif, avec une lame incurvée et une poignée ergonomique, idéal pour une variété d\'applications tactiques.', 45],
            [12, 'Huntsman Fade', 44.99, 8, '/Images/H2.jpeg', NULL, 26, 'Le Huntsman Fade est une fusion audacieuse de style et de fonctionnalité, alliant un design élégant à une performance fiable. Un couteau mélangeant les couleurs le rend plus sublime.', 54]

        );

        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);

        // Parcours le tableau des produits et les ajoute à la BDD
        foreach($tableaudesproduits as $produit){

            // Exécuter la requête
            $sql1->execute([
                $produit[0],
                $produit[1],
                $produit[2],
                $produit[3],
                $produit[4],
                $produit[5],
                $produit[6],
                $produit[7],
                $produit[8]
            ]);
        }
    }


}
?>
