<!DOCTYPE html>
<html lang="fr-FR">
    <head>
        <title>CSGOSHOP</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <!-- inclus bootstrap via CDN = Content Delivery Network (réseau de distribution de contenu) -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <!-- inclus style.css -->
        <link href="<?php ROOT?>/view/CSS/style.css" rel="stylesheet">
        <link href="<?php ROOT?>/view/CSS/panier.css" rel="stylesheet">

    </head>
    <body class="custom-body">
            <!-- Navbar -->
            <?php include_once ROOT.'/view/template/navbar.php'; ?>

            <!-- Animation défilé d'un texte -->
            <div class="border-bottom border-5 border-gradient bg-dark">
                <marquee class="fs-3 fw-medium" behavior="scroll" direction="left" style="color: white">🏷️LES SOLDES SONT ARRIVÉS ❗ C'est le moment de se faire plaisir ❗🏷️ | 💸 PROMO -80% SUR LE PRODUIT STILLETO FADE 💸 | &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; |⚠️ ATTENTION NOUS AVONS PLUS DE STOCK POUR LA KARAMBIT BLADE ❗ ⚠️</marquee>
            </div>
            
            <!-- Carousel de la page d'acceuil -->
            <div id="carouselExampleDark" class="carousel carousel-dark slide border-carousel">
                <div class="carousel-indicators">
                    <button type="button" data-bs-target="#carouselExampleDark" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Slide 1"></button>
                    <button type="button" data-bs-target="#carouselExampleDark" data-bs-slide-to="1" aria-label="Slide 2"></button>
                    <button type="button" data-bs-target="#carouselExampleDark" data-bs-slide-to="2" aria-label="Slide 3"></button>
                </div>
                <div class="carousel-inner">
                    <!-- 1ère image -->
                    <div class="carousel-item active" data-bs-interval="10000">
                    <img src="<?php ROOT?>/public/Images/Vitrine/K4V.jpg" class="d-block w-100" alt="...">
                    <div class="carousel-caption d-none d-md-block">
                        <h5>🔥Meilleure Vente !🔥</h5>
                        <p>La Karambite Lore est le couteau le plus vendu sur CSGOSHOP.</p>
                    </div>
                    </div>
                    <!-- 2ème image -->
                    <div class="carousel-item" data-bs-interval="2000">
                    <img src="<?php ROOT?>/public/Images/Vitrine/K1V.jpg" class="d-block w-100" alt="...">
                    <div class="carousel-caption d-none d-md-block">
                        <h5>Nouveau Produit !</h5>
                        <p>La Karambite Fade vien de sortir, n'attendez pas 😉.</p>
                    </div>
                    </div>
                    <!-- 3ème image -->
                    <div class="carousel-item">
                    <img src="<?php ROOT?>/public/Images/Vitrine/S1V.jpg" class="d-block w-100" alt="...">
                    <div class="carousel-caption d-none d-md-block">
                        <h5>Grosse réduction !📉</h5>
                        <p>C'est les soldes d'hiver et la Stilleto Fade est l'une des meilleures réduction !</p>
                    </div>
                    </div>
                </div>
                <!-- Les boutons Next and Previous -->
                <button class="carousel-control-prev" type="button" data-bs-target="#carouselExampleDark" data-bs-slide="prev">
                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">Previous</span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#carouselExampleDark" data-bs-slide="next">
                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">Next</span>
                </button>
            </div>

        <form action="" method="GET">
            <!-- Carte des produits recommandé -->
            <div class="container border-gradient text-center mt-5">
                <h1 class="link-underline">Recommandation</h1>
                <div class="row mt-5 mb-5">
                    
                <?php
                        $produits = DBManagerProduits::getLesProduits();
                        // Pour chaque produits on les affiches dans le HTML
                        $htmlrecommandation = '';
                        foreach($produits as $produit){
                            
                            if(DBManagerProduits::getProduitSelect("Bayonnet Autotronic")->getNom() == $produit->getNom() ||
                            DBManagerProduits::getProduitSelect("Karambit Fade")->getNom() == $produit->getNom() ||
                            DBManagerProduits::getProduitSelect("Gut Fade")->getNom() == $produit->getNom() ||
                            DBManagerProduits::getProduitSelect("Karambit Lore")->getNom() == $produit->getNom()){
                                $htmlrecommandation .= '                <div class="card shadow-card me-4 mt-5" style="width: 18rem;">
                                <img src="/public'. $produit->getImageUrl() .'" class="card-img-top" alt="...">
                                <div class="card-body">
                                    <h5 class="card-title">'. $produit->getNom() .'</h5>
                                    <p class="card-text text-center fs-4 fw-lighter">- '. $produit->getPrixUnit() .'€ -</p>
                                    <a href="/PageProduits/'. urlencode($produit->getNom()) .'/" class="btn btn-outline-dark mx-4" name="Produit" value="'. $produit->getNom() .'">
                                    Voir le produit <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                    <path fill-rule="evenodd" d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8"/>
                                  </svg></a>
                                </div>
                            </div>';
                            }
                        }


                        echo $htmlrecommandation;
                    
                    ?>
                </div>
            </div>

            <!-- Carte des produits à la une ! -->
            <div class="container mt-5">
                <h1>À la une</h1>
                <div class="row">
                    <div class="col-3 mt-4 custom-form">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" role="switch" name="selectmulti[]" value="Tous" id="filterAll" checked>
                                <label class="form-check-label" for="flexSwitchCheckDefault">Tous</label>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" role="switch" name="selectmulti[]" value="VenteFlash" id="filterVenteFlash">
                                <label class="form-check-label" for="flexSwitchCheckChecked">VenteFlash</label>
                            </div>
                    </div>
                    <div class="col-2 mt-4 custom-form-checkbox">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="selectmulti[]" value="Karambite" id="flexRadioDefault1">
                            <label class="form-check-label" for="flexRadioDefault1">
                            Karambit
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="selectmulti[]" value="Bayonnet" id="flexRadioDefault1">
                            <label class="form-check-label" for="flexRadioDefault1">
                                Bayonet
                            </label>
                        </div>
                    </div>
                    <div class="col-2 mt-4 custom-form-checkbox">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="selectmulti[]" value="Gut" id="flexRadioDefault1">
                            <label class="form-check-label" for="flexRadioDefault1">
                                Gut
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="selectmulti[]" value="Huntsman" id="flexRadioDefault1">
                            <label class="form-check-label" for="flexRadioDefault1">
                            Huntsman
                            </label>
                        </div>
                    </div>
                    <div class="col-2 mt-4 custom-form-checkbox">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="selectmulti[]" value="Stiletto" id="flexRadioDefault1"> <!-- Le selectmulti[] permet de faire un choix multiple et donc de stocker les valeur dans un tableau -->
                            <label class="form-check-label" for="flexRadioDefault1">
                            Stiletto
                            </label>
                        </div>
                    </div>
                    <div class="col-1 me-1 mt-4">
                        <div class="form-check">
                            <button class="btn btn-outline-dark rounded-pill" type="submit">Rechercher</button>
                        </div>
                    </div>
                </div>

                <div id="liste-produits" class="row text-center mt-2">

                    <?php 
                        // Récupère les filtres de l'URL ou définit 'Tous' par défaut
                        $filtres = isset($_GET['selectmulti']) ? $_GET['selectmulti'] : ['Tous'];

                        // récupère les produits de par les filtres
                        $produitsfiltre = DBManagerProduits::getProduitfiltre($filtres);

                        // Pour chaque produits on les affiches dans le HTML
                        $htmlproduits = '';
                        foreach($produitsfiltre as $produit){

                            // On vérifie si le produit qu'on recherche et bien dans la liste si oui alors on ajoute une réduction ou autres...
                            $addhtml = '';

                            if(DBManagerProduits::getProduitSelect("Gut Fade")->getNom() == $produit->getNom()){
                                $addhtml = '<span class="badge bg-danger" style="width:100%">PRIX RÉDUIT 📉 : -20% sur ce produit !</span>';
                            }

                            if(DBManagerProduits::getProduitSelect("Stiletto Fade")->getNom() == $produit->getNom()){
                                $addhtml = '<span class="badge bg-danger" style="width:100%">💥PRIX CHOC : -55% sur ce produit !🔥</span>';
                            }

                            if(DBManagerProduits::getProduitSelect("Karambit Blade")->getNom() == $produit->getNom()){
                                $addhtml = '<span class="badge bg-dark ms-5" style="width:60%">❌ Hors stock ❌</span>';
                                $htmlproduits .= '                <div class="card shadow-card me-4 mt-5" style="width: 18rem;">'. $addhtml .'
                                <img src="/public'. $produit->getImageUrl() .'" class="card-img-top" alt="...">
                                <div class="card-body">
                                    <h5 class="card-title">'. $produit->getNom() .'</h5>
                                    <p class="card-text text-center fs-4 fw-lighter text-decoration-line-through"> '. $produit->getPrixUnit() .'€ </p>
                                    <a href="/PageProduits/'. urlencode($produit->getNom()) .'" class="btn btn-outline-dark mx-4" name="Produit" value="'. $produit->getNom() .'">
                                    Voir le produit <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                    <path fill-rule="evenodd" d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8"/>
                                  </svg></a>
                                </div>
                            </div>';
                            }
                            
                            else{
                                $htmlproduits .= '                <div class="card shadow-card me-4 mt-5" style="width: 18rem;">'. $addhtml .'
                                <img src="/public'. $produit->getImageUrl() .'" class="card-img-top" alt="...">
                                <div class="card-body">
                                    <h5 class="card-title">'. $produit->getNom() .'</h5>
                                    <p class="card-text text-center fs-4 fw-lighter">- '. $produit->getPrixUnit() .'€ -</p>
                                    <a href="/PageProduits/'. urlencode($produit->getNom()) .'" class="btn btn-outline-dark mx-4" name="Produit" value="'. $produit->getNom() .'">
                                    Voir le produit <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                    <path fill-rule="evenodd" d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8"/>
                                  </svg></a>
                                </div>
                            </div>';
                            }
                        }


                        echo $htmlproduits;
                    
                    ?>
                    
                </div>
            </div>
        </form>

        <!-- Footer -->
        <?php include_once ROOT.'/view/template/footer.php'; ?>

        <!-- Modal -->
        <?php include_once ROOT.'/view/template/panier.php'; ?>
        


        <!-- Chargement javascript -->
        <!-- js pour bootstrap -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script>
        <script src="<?php ROOT?>/Js/ModifQtePanier.js"></script>

    </body>
</html>
