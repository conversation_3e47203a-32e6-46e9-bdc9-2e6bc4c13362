<?php
class PageSecuriteBackOfficeController extends Controller{


    public static function index($params){

        // Vérifie si l'utilisateur est connecté en tant qu'administrateur
        if (!isset($_SESSION['User'])) {
            // Redirige vers la page de connexion si non connecté en tant qu'administrateur
            header("Location: /BO-Admin/");
            exit();
        }
        
        $params['html'] = '';

        if (isset($_SESSION['User'])) {

            $params['user'] = DBManagerAdmin::getAdminByName2(htmlspecialchars($_SESSION['User']));
            $params['username'] = $params['user']->getUtilisateur();
            $params['prenom'] = $params['user']->getPrenom();
            $params['nom'] = $params['user']->getNom();
            $params['email'] = $params['user']->getEmail();
            $params['mobile'] = $params['user']->getTel();
            $params['password'] = $params['user']->getMdp();

            if ($_SERVER['REQUEST_METHOD'] == 'POST') {
                $updated = false;

                // Vérifie et met à jour l'utilisateur uniquement si une valeur a été saisie
                if(!empty($_POST['User']) && $_POST['User'] !== $params['username']) {
                    $utilisateur = htmlspecialchars($_POST['User']);
                    DBManagerAdmin::ModifUserNameAdmin($utilisateur, $_SESSION['User']);
                    $_SESSION['User'] = $utilisateur; // Met à jour la session avec le nouveau nom d'utilisateur
                    $updated = true;
                }

                // Vérifie et met à jour le prénom uniquement si une valeur a été saisie
                if(!empty($_POST['Prenom']) && $_POST['Prenom'] !== $params['prenom']) {
                    $prenom_uti = htmlspecialchars($_POST['Prenom']);
                    DBManagerAdmin::ModifFirstNameAdmin($prenom_uti, $_SESSION['User']);
                    $updated = true;
                }

                // Vérifie et met à jour le nom uniquement si une valeur a été saisie
                if(!empty($_POST['Nom']) && $_POST['Nom'] !== $params['nom']) {
                    $nom_uti = htmlspecialchars($_POST['Nom']);
                    DBManagerAdmin::ModifNameAdmin($nom_uti, $_SESSION['User']);
                    $updated = true;
                }

                // Vérifie et met a jour l'email uniquement si une valeur a été saisie
                if(!empty($_POST['email']) && $_POST['email'] !== $params['email']) {
                    $email_uti = htmlspecialchars($_POST['email']);
                    DBManagerAdmin::ModifEmailAdmin($email_uti, $_SESSION['User']);
                    $updated = true;
                }

                // Vérifie et met à jour le téléphone uniquement si une valeur a été saisie
                if(!empty($_POST['mobile']) && $_POST['mobile'] !== $params['mobile']) {
                    $mobile_uti = htmlspecialchars($_POST['mobile']);
                    DBManagerAdmin::ModifMobileAdmin($mobile_uti, $_SESSION['User']);
                    $updated = true;
                }

                // Vérifie et met a jour le mot de passe uniquement si une valeur a été saisie et confirmé
                if(!empty($_POST['mdp2']) && !empty($_POST['mdpconfirmation'])) {
                    $mdp_uti = htmlspecialchars($_POST['mdp2']);
                    $mdpconf_uti = htmlspecialchars($_POST['mdpconfirmation']);
                    if($mdp_uti === $mdpconf_uti) {
                        DBManagerAdmin::ModifPasswordAdmin($mdp_uti, $_SESSION['User']);
                        $updated = true;
                    }
                }

                if($updated) {
                    $params['html'] .= '<p class="fs-5 text-center" style="color: green;">Modification effectuée avec succès !</p>';
                } else {
                    $params['html'] .= '<p class="fs-5 text-center" style="color: red;">Aucune modification n\'a été apportée.</p>';
                }
            }
        }
        
        // appelle la vue
        $view = ROOT."/view/BackOffice/pagesecuriteBackOffice.php";
        self::render($view, $params);
    }
}
?>