<!DOCTYPE html>
<html lang="fr-FR">
    <head>
        <title>CSGOSHOP</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <!-- inclus bootstrap via CDN = Content Delivery Network (réseau de distribution de contenu) -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <!-- inclus style.css -->
        <link href="<?php ROOT?>/view/CSS/backoffice.css" rel="stylesheet">

    </head>
    <body class="custom-body">

        <!-- Cases paramètre -->
        <div class="container mt-5">
        </div>

        <!-- Les cards pour paramèter son compte -->
        <div class="container">
            <!-- Ligne n°1 -->
            <div class="row mt-5">
                <!-- Gestion des produits -->
                <div class="col">
                    <a href="/BO-Admin/Accueil/gestionproduits/" class="card" style="text-decoration: none;">
                        <p class="fs-3 fw-semibold mt-2 text-center">Gestion des produits.</p>
                        <div class="row">
                            <div class="col-4">
                                <p class="round ms-3"><svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="currentColor" class="bi bi-plus-circle" viewBox="0 0 16 16">
                                <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16"/>
                                <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4"/>
                                </svg></p>
                            </div>
                            <div class="col-6 me-1">
                                <p>Ajoutez, modifier, Supprimer un Produit</p>
                            </div>
                        </div>
                    </a>
                </div>
                <!-- Gestion des comptes -->
                <div class="col">
                    <a href="/BO-Admin/Accueil/gestioncompte/" class="card" style="text-decoration: none;">
                        <p class="fs-3 fw-semibold mt-2 text-center">Gestion des comptes</p>
                        <div class="row">
                            <div class="col-4">
                                <p class="round ms-3"><svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="currentColor" class="bi bi-person-add" viewBox="0 0 16 16">
                                <path d="M12.5 16a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7m.5-5v1h1a.5.5 0 0 1 0 1h-1v1a.5.5 0 0 1-1 0v-1h-1a.5.5 0 0 1 0-1h1v-1a.5.5 0 0 1 1 0m-2-6a3 3 0 1 1-6 0 3 3 0 0 1 6 0M8 7a2 2 0 1 0 0-4 2 2 0 0 0 0 4"/>
                                <path d="M8.256 14a4.5 4.5 0 0 1-.229-1.004H3c.001-.246.154-.986.832-1.664C4.484 10.68 5.711 10 8 10q.39 0 .74.025c.226-.341.496-.65.804-.918Q8.844 9.002 8 9c-5 0-6 3-6 4s1 1 1 1z"/>
                                </svg></p>
                            </div>
                            <div class="col-7 me-1">
                                <p>Modifier les privilèges des utilisateurs.</p>
                            </div>
                        </div>
                    </a>
                </div>
                <!-- Gestion des commandes -->
                <div class="col">
                    <a href="/BO-Admin/Accueil/gestioncommandes/" class="card" style="text-decoration: none;">
                        <p class="fs-3 fw-semibold mt-2 text-center">Gestion des commandes</p>
                        <div class="row">
                            <div class="col-4">
                                <p class="round ms-3"><svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="currentColor" class="bi bi-box-fill" viewBox="0 0 16 16">
                                <path fill-rule="evenodd" d="M15.528 2.973a.75.75 0 0 1 .472.696v8.662a.75.75 0 0 1-.472.696l-7.25 2.9a.75.75 0 0 1-.557 0l-7.25-2.9A.75.75 0 0 1 0 12.331V3.669a.75.75 0 0 1 .471-.696L7.443.184l.004-.001.274-.11a.75.75 0 0 1 .558 0l.274.11.004.001zm-1.374.527L8 5.962 1.846 3.5 1 3.839v.4l6.5 2.6v7.922l.5.2.5-.2V6.84l6.5-2.6v-.4l-.846-.339Z"/>
                                </svg></p>
                            </div>
                            <div class="col-7 me-1">
                                <p>Visualiser les commandes.</p>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <!-- Ligne n°2-->
            <div class="row mt-5">
                <!-- Gestion de la sécurité du compte -->
                <div class="col">
                    <a href="/BO-Admin/Accueil/pagesecurite/" class="card" style="text-decoration: none;">
                        <p class="fs-4 fw-semibold mt-2 text-center">Connexion & Sécurité</p>
                        <div class="row">
                            <div class="col-4">
                                <p class="round ms-3"><svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="currentColor" class="bi bi-shield-fill-check" viewBox="0 0 16 16">
                                <path fill-rule="evenodd" d="M8 0c-.69 0-1.843.265-2.928.56-1.11.3-2.229.655-2.887.87a1.54 1.54 0 0 0-1.044 1.262c-.596 4.477.787 7.795 2.465 9.99a11.8 11.8 0 0 0 2.517 2.453c.386.273.744.482 1.048.625.28.132.581.24.829.24s.548-.108.829-.24a7 7 0 0 0 1.048-.625 11.8 11.8 0 0 0 2.517-2.453c1.678-2.195 3.061-5.513 2.465-9.99a1.54 1.54 0 0 0-1.044-1.263 63 63 0 0 0-2.887-.87C9.843.266 8.69 0 8 0m2.146 5.146a.5.5 0 0 1 .708.708l-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7.5 7.793z"/>
                                </svg></p>
                            </div>
                            <div class="col-7 me-1">
                                <p>Modifier le mot de passe, adresse mail, numéro de téléphone...</p>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        </div>

        <!-- Chargement javascript -->
        <!-- js pour bootstrap -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script>

    </body>
</html>
