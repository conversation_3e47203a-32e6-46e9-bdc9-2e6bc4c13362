<!DOCTYPE html>
<html lang="fr-FR">
    <head>
        <title>CSGOSHOP</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <!-- inclus bootstrap via CDN = Content Delivery Network (réseau de distribution de contenu) -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
        <!-- inclus style.css -->
        <link href="<?php ROOT?>/view/CSS/style.css" rel="stylesheet">
        <link href="<?php ROOT?>/view/CSS/style6.css" rel="stylesheet">
        <link href="<?php ROOT?>/view/CSS/panier.css" rel="stylesheet">

    </head>
    <body class="custom-body">
        <div class="page-wrapper">

            <!-- Contenu principal -->
            <div class="container mt-5">
                <!-- Progress Steps -->
                <div class="progress-steps mb-5">
                    <div class="step active">
                        <div class="step-icon">
                            <i class="bi bi-cart-fill"></i>
                            <span class="step-number">1</span>
                        </div>
                        <span class="step-label">Panier</span>
                    </div>
                    <div class="step">
                        <div class="step-icon">
                            <i class="bi bi-truck"></i>
                            <span class="step-number">2</span>
                        </div>
                        <span class="step-label">Livraison</span>
                    </div>
                    <div class="step">
                        <div class="step-icon">
                            <i class="bi bi-credit-card"></i>
                            <span class="step-number">3</span>
                        </div>
                        <span class="step-label">Paiement</span>
                    </div>
                </div>

                <form action="" method="GET">
                <!-- Tableau du panier -->
                <div class="cart-container">
                    <?php
                    try {
                        $idClient = DBManagerClient::getClientByName2($_SESSION['user'])->getID();
                        $total = 0;
                        
                        echo '<div class="cart-items">';
                        
                        foreach (DBManagerPanier::getPanierByUser($idClient) as $panier) {
                            $produit = DBManagerProduits::getProduitByID($panier->getIdProduit());
                            $prix = $produit->getPrixUnit();
                            $sousTotal = $prix * $panier->getQte();
                            $total += $sousTotal;
                            
                            echo '<div class="cart-item">
                                    <div class="item-image">
                                        <img src="/public' . $produit->getImageUrl() . '" alt="' . $produit->getNom() . '">
                                    </div>
                                    <div class="item-details">
                                        <h3>' . $produit->getNom() . '</h3>
                                        <div class="item-price">' . number_format($prix, 2) . ' €</div>
                                    </div>
                                    <div class="item-quantity">
                                        <input name="ProduitPanierQTE" value="' . $panier->getQte() . '">
                                        <button name="BtnMoins" value="' . $produit->getID() . '" type="submit" class="qty-btn minus">-</button>
                                        <span>' . $panier->getQte() . '</span>
                                        <button name="BtnPlus" value="' . $produit->getID() . '" type="submit" class="qty-btn plus">+</button>
                                    </div>
                                    <div class="item-total">' . number_format($sousTotal, 2) . ' €</div>
                                    <button name="SupprimerPanier" value="' . $produit->getID() . '" class="item-remove" type="submit">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>';
                        }
                        
                        echo '<div class="cart-summary">
                                <div class="summary-row">
                                    <span>Total</span>
                                    <span class="total-price">' . number_format($total, 2) . ' €</span>
                                </div>
                              </div>
                            </div>';
                        
                    } catch(Exception $e) {
                        echo '<div class="empty-cart">
                                <i class="bi bi-cart-x"></i>
                                <p>Votre panier est vide</p>
                                <a href="/" class="btn btn-primary">Découvrez nos produits</a>
                              </div>';
                    }
                    ?>
                </div>

                <!-- Boutons de navigation -->
                <div class="d-flex justify-content-between mt-4 mb-5">
                    <a href="/" class="btn btn-outline-dark">
                        <i class="bi bi-arrow-left"></i> Retour
                    </a>
                    <a href="/Livraison/" class="btn btn-primary">
                        Continuer vers la livraison <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
                </form>
            </div>
        </div>

        <!-- Chargement javascript -->
        <!-- js pour bootstrap -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script>


    </body>
</html>
