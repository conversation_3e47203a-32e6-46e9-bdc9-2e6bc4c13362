<!DOCTYPE html>
<html lang="fr-FR">
    <head>
        <title>CSGOSHOP</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <!-- inclus bootstrap via CDN = Content Delivery Network (réseau de distribution de contenu) -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <!-- inclus style.css -->
        <link href="<?php ROOT?>/view/CSS/style.css" rel="stylesheet">
        <link href="<?php ROOT?>/view/CSS/panier.css" rel="stylesheet">

    </head>
    <body class="custom-body">
        <!-- Navbar -->
        <?php include_once ROOT.'/view/template/navbar.php'; ?>

    <div class="page-wrapper">

        <form method="GET" action="/PageProduits/<?php echo urlencode($_GET['Produit']); ?>/ajoutpanier" class="content">

            <!-- Page du produit -->
            <div class="container mt-5">
                <?php
                    
                    //Selection du produit recherché
                    $produit = DBManagerProduits::getProduitSelect($_GET['Produit']);
                    
                    // Condition qui change le html si le produit est en stock ou non
                    $stockornotstock = '<h3 style="color: green;">En Stock</h3>';
                    $disabledbtn = '';
                    if($produit->getStock() == 0){
                        $stockornotstock = '<h3 style="color: red;">Hors Stock</h3>';
                        $disabledbtn = 'disabled';
                    }

                    //Boucle pour modifier la quantité du select
                    $htmlselect = '';
                    for($i=1; $i <= $produit->getStock(); $i++){
                        $htmlselect .= '<option name="Quantite" value="'.$i.'">'.$i.'</option>';
                    }

                    $html = '

                    <div class="row">
                    <div class="col-4">
                        <div class="card shadow-card me-4" style="width: 24rem;">
                            <div class="card-body">
                                <img src="/public'. $produit->getImageUrl() .'" class="card-img-top" alt="...">
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <h1>'. $produit->getNom() .'</h1>
                        <p class="fs-5">'. $produit->getDescp() .'</p>
                        <h2>Prix : '. $produit->getPrixUnit() .' €</h2>
                        <h6>Il reste '. $produit->getStock() .' exemplaires.</h6>';
                        // Success d'ajout du produit dans le panier
                        if (isset($_SESSION['success_message'])) {
                            $html .= '<div class="alert alert-success text-center">' . $_SESSION['success_message'] . '</div>';
                        }
                        $html .= '
                    </div>
                    <div class="col-1 align-self-center">
                        <div class="card shadow-card me-4" style="width: 24rem;">
                                <div class="card-body ms-4">'
                                . $stockornotstock .
                                '
                                    <p>Choisissez la quantité : </p>
                                    <select style="width: 35%;" name="Quantite" class="form-select mt-2 mb-4">
                                        '. $htmlselect .'
                                    </select>
                                    <p>Puis ajoutez :</p>
                                    <button type="submit" class="btn btn-outline-dark mb-2" 
                                            name="ProduitPanier" 
                                            value="'. $produit->getNom() .'" 
                                            '. $disabledbtn .'>
                                        Ajouter au panier 
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="18" fill="currentColor"
                                            class="bi bi-cart" viewBox="0 0 16 18">
                                            <path d="M0 1.5A.5.5 0 0 1 .5 1H2a.5.5 0 0 1 .485.379L2.89 3H14.5a.5.5 0 0 1 .491.592l-1.5 8A.5.5 0 0 1 13 12H4a.5.5 0 0 1-.491-.408L2.01 3.607 1.61 2H.5a.5.5 0 0 1-.5-.5M3.102 4l1.313 7h8.17l1.313-7zM5 12a2 2 0 1 0 0 4 2 2 0 0 0 0-4m7 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4m-7 1a1 1 0 1 1 0 2 1 1 0 0 1 0-2m7 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2"/>
                                        </svg>
                                    </button>
                                </div>
                        </div>
                    </div>
                </div>

                    ';

                    echo $html;
                    
                ?>
    
            </div>
        </form>
    </div>

        <!-- Footer -->
        <?php include_once ROOT.'/view/template/footer.php'; ?>


        <!-- Modal -->
        <?php include_once ROOT.'/view/template/panier.php'; ?>


        <!-- Chargement javascript -->
        <!-- js pour bootstrap -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script>
        <script src="<?php ROOT?>/Js/ModifQtePanier.js"></script>
        
    </body>
</html>
