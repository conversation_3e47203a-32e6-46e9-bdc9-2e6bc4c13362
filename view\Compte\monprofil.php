<!DOCTYPE html>
<html lang="fr-FR">
    <head>
        <title>CSGOSHOP</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <!-- inclus bootstrap via CDN = Content Delivery Network (réseau de distribution de contenu) -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <!-- inclus style.css -->
        <link href="<?php ROOT?>/view/CSS/style4.css" rel="stylesheet">
        <link href="<?php ROOT?>/view/CSS/panier.css" rel="stylesheet">

    </head>
    <body class="custom-body">
        <!-- Wrapper principal -->
        <div class="page-wrapper">
            <!-- Navbar -->
            <?php include_once ROOT.'/view/template/navbar.php'; ?>

            <!-- Cases paramètre -->
            <div class="container">
                <?php
                    if(isset($_SESSION['user'])){
                        echo '<h1 class="mt-3">Bienvenue sur votre compte '.$_SESSION['user'].'</h1>';
                    }
                ?>
            </div>

            <!-- Contenu principal -->
            <div class="content">
                <!-- Les cards pour paramèter son compte -->
                <div class="container">
                    <!-- Ligne n°1 -->
                    <div class="row mt-5">
                        <div class="col">
                            <a href="#" class="card" data-bs-toggle="modal" data-bs-target="#Modal" style="text-decoration: none;">
                                <p class="fs-3 fw-semibold mt-2 text-center">Service Client</p>
                                <div class="row">
                                    <div class="col-4">
                                        <p class="round ms-3"><svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="currentColor" class="bi bi-telephone-outbound-fill" viewBox="0 0 16 16">
                                        <path fill-rule="evenodd" d="M1.885.511a1.745 1.745 0 0 1 2.61.163L6.29 2.98c.329.423.445.974.315 1.494l-.547 2.19a.68.68 0 0 0 .178.643l2.457 2.457a.68.68 0 0 0 .644.178l2.189-.547a1.75 1.75 0 0 1 1.494.315l2.306 1.794c.829.645.905 1.87.163 2.611l-1.034 1.034c-.74.74-1.846 1.065-2.877.702a18.6 18.6 0 0 1-7.01-4.42 18.6 18.6 0 0 1-4.42-7.009c-.362-1.03-.037-2.137.703-2.877zM11 .5a.5.5 0 0 1 .5-.5h4a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-1 0V1.707l-4.146 4.147a.5.5 0 0 1-.708-.708L14.293 1H11.5a.5.5 0 0 1-.5-.5"/>
                                        </svg></p>
                                    </div>
                                    <div class="col-6 me-1">
                                        <p>Contactez nous par téléphone, mail ou chat.</p>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col">
                            <a href="/MonProfil/Commandes/" class="card" style="text-decoration: none;">
                                <p class="fs-3 fw-semibold mt-2 text-center">Commandes</p>
                                <div class="row">
                                    <div class="col-4">
                                        <p class="round ms-3"><svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="currentColor" class="bi bi-box-fill" viewBox="0 0 16 16">
                                        <path fill-rule="evenodd" d="M15.528 2.973a.75.75 0 0 1 .472.696v8.662a.75.75 0 0 1-.472.696l-7.25 2.9a.75.75 0 0 1-.557 0l-7.25-2.9A.75.75 0 0 1 0 12.331V3.669a.75.75 0 0 1 .471-.696L7.443.184l.004-.001.274-.11a.75.75 0 0 1 .558 0l.274.11.004.001zm-1.374.527L8 5.962 1.846 3.5 1 3.839v.4l6.5 2.6v7.922l.5.2.5-.2V6.84l6.5-2.6v-.4l-.846-.339Z"/>
                                        </svg></p>
                                    </div>
                                    <div class="col-7 me-1">
                                        <p>Suivre la commandes, annuler, rembourser.</p>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col">
                            <a href="/MonProfil/PageAdresse/" class="card" style="text-decoration: none;">
                                <p class="fs-3 fw-semibold mt-2 text-center">Adresses</p>
                                <div class="row">
                                    <div class="col-4">
                                        <p class="round ms-3"><svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="currentColor" class="bi bi-house-door-fill" viewBox="0 0 16 16">
                                        <path d="M6.5 14.5v-3.505c0-.245.25-.495.5-.495h2c.25 0 .5.25.5.5v3.5a.5.5 0 0 0 .5.5h4a.5.5 0 0 0 .5-.5v-7a.5.5 0 0 0-.146-.354L13 5.793V2.5a.5.5 0 0 0-.5-.5h-1a.5.5 0 0 0-.5.5v1.293L8.354 1.146a.5.5 0 0 0-.708 0l-6 6A.5.5 0 0 0 1.5 7.5v7a.5.5 0 0 0 .5.5h4a.5.5 0 0 0 .5-.5"/>
                                        </svg></p>
                                    </div>
                                    <div class="col-7 me-1">
                                        <p>Modifier l'adresse de livraison.</p>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                    <!-- Ligne n°2-->
                    <div class="row mt-5">
                        <div class="col">
                            <a href="/MonProfil/PageSecurite/" class="card" style="text-decoration: none;">
                                <p class="fs-4 fw-semibold mt-2 text-center">Connexion & Sécurité</p>
                                <div class="row">
                                    <div class="col-4">
                                        <p class="round ms-3"><svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="currentColor" class="bi bi-shield-fill-check" viewBox="0 0 16 16">
                                        <path fill-rule="evenodd" d="M8 0c-.69 0-1.843.265-2.928.56-1.11.3-2.229.655-2.887.87a1.54 1.54 0 0 0-1.044 1.262c-.596 4.477.787 7.795 2.465 9.99a11.8 11.8 0 0 0 2.517 2.453c.386.273.744.482 1.048.625.28.132.581.24.829.24s.548-.108.829-.24a7 7 0 0 0 1.048-.625 11.8 11.8 0 0 0 2.517-2.453c1.678-2.195 3.061-5.513 2.465-9.99a1.54 1.54 0 0 0-1.044-1.263 63 63 0 0 0-2.887-.87C9.843.266 8.69 0 8 0m2.146 5.146a.5.5 0 0 1 .708.708l-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7.5 7.793z"/>
                                        </svg></p>
                                    </div>
                                    <div class="col-7 me-1">
                                        <p>Modifier le mot de passe, adresse mail, numéro de téléphone...</p>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- MODAL CHATBOX -->
                <div class="modal" id="Modal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">CSGOSHOP CHATBOX</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <iframe src="https://chatthing.ai/bots/6dcad6c5-c43a-4f24-b49f-e2313d6643a7/embed" width="100%" height="550" frameborder="0" allow="clipboard-write"></iframe>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <?php include_once ROOT.'/view/template/footer.php'; ?>

        <!-- Modal -->
        <?php include_once ROOT.'/view/template/panier.php'; ?>


        <!-- Chargement javascript -->
        <!-- js pour bootstrap -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script>
        <script src="<?php ROOT?>/Js/ModifQtePanier.js"></script>
    </body>
</html>

