<!DOCTYPE html>
<html lang="fr-FR">
    <head>
        <title>CSGOSHOP</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <!-- inclus bootstrap via CDN = Content Delivery Network (réseau de distribution de contenu) -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <!-- inclus style.css -->
        <link href="<?php ROOT?>/view/CSS/style2.css" rel="stylesheet">

    </head>
    <body>    
        <div class="container">
            <div class="row">
                <!-- Bouton de retour -->
                <div class="col col-lg-3 mt-5">
                    <a href="/" class="text-dark underline-change"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-left" viewBox="0 0 16 18">
                    <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8"/>
                    </svg> Retour</a>
                </div>

                <!-- Logo CSGOSHOP -->
                <div class="col text-center">
                    <img src="<?php ROOT?>/public/Images/CSGOSHOP LOGO.png" width="210" alt="">
                </div>

                <div class="col col-lg-3"></div>
            </div>
            
        </div>
        

        <div class="container position-absolute top-50 start-50 translate-middle">
            <!-- Formulaire de connexion -->
            <form action="/Connexion/" method="POST">
                <div class="row">
                        <div class="col col-lg-3">
                        </div>
                    
                        <div class="col shadow-sm border rounded-3 bg-light">
                            <h1 class="text-center mt-3">Connexion</h1>

                            <!-- Erreur de connexion -->
                            <?php if (isset($params['error'])){
                                echo '<div class="alert alert-danger text-center">'.$params['error'].'</div>';
                            }
                            ?>
                            
                            <!-- Information de l'utilisateur -->
                            <div class="text-center mt-5 ps-5 pe-5">
                                <input type="text" class="form-control" id="user" name="id" placeholder="Nom d'utilisateur">
                            </div>

                            <!-- Mot de passe de l'utilisateur -->
                            <div class="text-center mt-2 ps-5 pe-5">
                                <input type="password" class="form-control" id="password" name="password" placeholder="Mot de passe">
                            </div>

                            <!-- Bouton de connexion -->
                            <div class="text-center mt-5 mb-3">
                                <input class="btn btn-outline-dark" type="submit" id="connexion" value="Se connecter">
                            </div>
                        </div>

                        <div class="col col-lg-3">
                        </div>
                </div>
                <!-- Lien d'inscription -->
                <div class="row text-center mt-3">
                    <a href="/Inscription/" class="text-dark underline-change2">Inscrivez-vous dès maintenant !</a>
                </div>
            </form>
        </div>

    </body>
</html>
