/* Progress Steps */
.progress-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 800px;
    margin: 3rem auto;
    position: relative;
    padding: 0 20px;
}

.progress-steps::before {
    content: '';
    position: absolute;
    top: 35px;
    left: 0;
    width: 100%;
    height: 3px;
    background: #e0e0e0;
    z-index: 1;
}

.progress-steps::after {
    content: '';
    position: absolute;
    top: 35px;
    left: 0;
    width: 66%;
    height: 3px;
    background: linear-gradient(65deg, #383F51, #1d0feb);
    z-index: 1;
    transition: width 0.3s ease;
}

.step {
    position: relative;
    z-index: 2;
    width: 70px;
    text-align: center;
}

.step-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    border: 3px solid #e0e0e0;
    transition: all 0.3s ease;
    font-size: 1.5rem;
}

.step.active .step-icon {
    border-color: #1d0feb;
    background: linear-gradient(65deg, #383F51, #1d0feb);
    color: white;
    box-shadow: 0 0 20px rgba(29, 15, 235, 0.3);
}

.step-label {
    margin-top: 1rem;
    font-weight: 600;
    color: #383F51;
    font-size: 0.9rem;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.step.active .step-label {
    opacity: 1;
    color: #1d0feb;
}

/* Formulaire de livraison */
.form-control {
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 12px;
    transition: all 0.3s ease;
    background: white;
}

.form-control:focus {
    border-color: #1d0feb;
    box-shadow: 0 0 0 0.2rem rgba(29, 15, 235, 0.15);
    outline: none;
}

.form-label {
    font-weight: 600;
    color: #383F51;
    margin-bottom: 0.5rem;
}

/* Boutons de navigation */
.btn-outline-dark {
    border: 2px solid #383F51;
    color: #383F51;
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-outline-dark:hover {
    background: #383F51;
    color: white;
    transform: translateY(-2px);
}

.btn-primary {
    background: linear-gradient(65deg, #383F51, #1d0feb);
    border: none;
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(29, 15, 235, 0.3);
}

/* Container principal */
.container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    margin-top: 2rem;
    margin-bottom: 2rem;
}
