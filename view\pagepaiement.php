<!DOCTYPE html>
<html lang="fr-FR">
    <head>
        <title>CSGOSHOP</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <!-- inclus bootstrap via CDN = Content Delivery Network (réseau de distribution de contenu) -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
        <!-- inclus style.css -->
        <link href="<?php ROOT?>/view/CSS/style.css" rel="stylesheet">
        <link href="<?php ROOT?>/view/CSS/style6bis2.css" rel="stylesheet">
        <link href="<?php ROOT?>/view/CSS/panier.css" rel="stylesheet">

    </head>
    <body class="custom-body">
        <!-- Wrapper principal -->
        <div class="page-wrapper">
            <!-- Contenu principal -->
            <main class="content">
                <div class="container mt-5">
                    <!-- Progress Steps -->
                    <div class="progress-steps mb-5">
                        <div class="step active">
                            <div class="step-icon">
                                <i class="bi bi-cart-fill"></i>
                                <span class="step-number">1</span>
                            </div>
                            <span class="step-label">Panier</span>
                        </div>
                        <div class="step active">
                            <div class="step-icon">
                                <i class="bi bi-truck"></i>
                                <span class="step-number">2</span>
                            </div>
                            <span class="step-label">Livraison</span>
                        </div>
                        <div class="step active">
                            <div class="step-icon">
                                <i class="bi bi-credit-card"></i>
                                <span class="step-number">3</span>
                            </div>
                            <span class="step-label">Paiement</span>
                        </div>
                    </div>

                    <!-- Formulaire de Paiement -->
                    <form id="form_paiement" action="/Paiement/" method="POST">
                        <div class="row justify-content-center">
                            <div class="col-md-6">
                                <h2 class="text-center">Informations de paiement</h2>
                                <div class="mb-3">
                                    <label for="payment_method" class="form-label">Méthode de paiement</label>
                                    <select class="form-control" id="payment_method" name="payment_method">
                                        <option value="">Choisissez une méthode de paiement</option>
                                        <option value="card">Carte Bancaire</option>
                                        <option value="paypal">PayPal</option>
                                    </select>
                                </div>

                                <!-- Champs pour Carte Bancaire -->
                                <div id="card_fields" style="display: none;">
                                    <div class="mb-3">
                                        <label for="card_number" class="form-label">Numéro de carte</label>
                                        <input type="text" class="form-control" id="card_number" name="card_number" maxlength="16" placeholder="1234 5678 9012 3456">
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="expiry_date" class="form-label">Date d'expiration</label>
                                            <input type="text" class="form-control" id="expiry_date" name="expiry_date" placeholder="MM/AA" maxlength="5">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="cvv" class="form-label">CVV</label>
                                            <input type="text" class="form-control" id="cvv" name="cvv" maxlength="3" placeholder="123">
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="card_name" class="form-label">Nom sur la carte</label>
                                        <input type="text" class="form-control" id="card_name" name="card_name" placeholder="JEAN DUPONT">
                                    </div>
                                </div>

                                <!-- Champs pour PayPal -->
                                <div id="paypal_fields" style="display: none;">
                                    <div class="mb-3">
                                        <label for="paypal_email" class="form-label">Email PayPal</label>
                                        <input type="email" class="form-control" id="paypal_email" name="paypal_email" placeholder="<EMAIL>">
                                    </div>
                                </div>

            <!-- Boutons de navigation -->
            <div class="d-flex justify-content-between mt-4 mb-5">
                <a href="/Livraison/" class="btn btn-outline-dark">
                    <i class="bi bi-arrow-left"></i> Retour
                </a>
                <!-- Bouton de paiement -->
                 <?php if (DBManagerPanier::VerifiePanier(DBManagerClient::getClientByName2($_SESSION['user'])->getID())) {
                    echo '<button type="button" id="button_paiement" class="btn btn-primary">
                    Finaliser la commande <i class="bi bi-arrow-right"></i>
                    </button>';
                    }
                    else {
                    echo '<button type="button" id="button_paiement" class="btn btn-primary" disabled>
                    Finaliser la commande <i class="bi bi-arrow-right"></i>
                    </button>';
                    }
                ?>
            </div>
        </div>
            </main>

            <!-- Footer -->
            <?php include_once ROOT.'/view/template/footer.php'; ?>
        </div>

        <!-- Chargement javascript -->
        <!-- js pour bootstrap -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script>
        <script src="<?php ROOT?>/Js/formPaiement.js"></script>

    </body>
</html>
