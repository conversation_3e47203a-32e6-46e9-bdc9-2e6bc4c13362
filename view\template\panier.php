        <!-- Modal -->
        <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="exampleModalLabel">Panier</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form action="" method="GET">
                <?php
                        // Vérifier si le panier est vide sinon il ajoute un article
                        if(DBManagerPanier::VerifiePanier(DBManagerClient::getClientByName2($_SESSION['user'])->getID())) {
                            // html qui affiche un tableau du panier
                            $htmlpanier = '<table id="table-panier" class="panier-table">
                                            <thead>
                                                <tr>
                                                    <th>Image</th>
                                                    <th>Produit</th>
                                                    <th>Prix</th>
                                                    <th>Quantité</th>
                                                    <th>Action</th>
                                                </tr>
                                            </thead>
                                            <tbody>';
                        
                            $idClient = DBManagerClient::getClientByName2($_SESSION['user'])->getID();
                            $total = 0;
                            
                            foreach (DBManagerPanier::getPanierByUser($idClient) as $panier) {
                                $produit = DBManagerProduits::getProduitByID($panier->getIdProduit());
                                $prix = $produit->getPrixUnit();
                                $total += $prix * $panier->getQte();
                                
                                $htmlpanier .= '<tr>';
                                $htmlpanier .= '<td><img src="/public' . $produit->getImageUrl() . '" alt="produit" width="50"></td>';
                                $htmlpanier .= '<td class="NomProduit" data-id="' . $produit->getID() . '">' . $produit->getNom() . '</td>';
                                $htmlpanier .= '<td class="price">' . number_format($prix, 2) . ' €</td>'; //number_format permet d'ajouter 2 chiffres après la virgule même si c'est un chiffre rond.
                                $htmlpanier .= '<td class="QteProduit" class="quantity">' . $panier->getQte() . '</td>';
                                $htmlpanier .= '<td>
                                                <button name="SupprimerPanier" value="' . $produit->getID() . '" type="Submit" class="item-remove">
                                                    <i class="bi bi-trash-fill"></i>
                                                </button>
                                                <button type="button" class="btn-edit" onclick="ModifierQtePanier(' . $produit->getID() . ')">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-pencil" viewBox="0 0 16 16">
                                                    <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325"/>
                                                    </svg>
                                               </button>
                                               </td>';
                                $htmlpanier .= '</tr>';
                            }
                            
                            $htmlpanier .= '</tbody>';
                            $htmlpanier .= '<tfoot><tr><td colspan="4" class="panier-total">Total : ' . number_format($total, 2) . ' €</td></tr></tfoot>';
                            $htmlpanier .= '</table>';
                            
                        } else {
                            $htmlpanier = '<div class="panier-empty">Votre panier est vide...</div>';
                        }
                        
                        echo $htmlpanier;

                        if(isset($_GET['SupprimerPanier'])){
                            DBManagerPanier::SupprimerProduitPanier($idClient, $_GET['SupprimerPanier']);
                            $_SESSION['message_success'] = 'Produit supprimé du panier avec succès !';
                            header("Location: /");
                            exit();
                        }

                        if(isset($_GET['ModifierQtePanier'])){
                            if(!empty($_GET['qte']) && is_numeric($_GET['qte']) && $_GET['qte'] > 0){
                                DBManagerPanier::ModifierQtePanier($idClient, $_GET['ModifierQtePanier'], htmlspecialchars($_GET['qte']));
                                
                                $_SESSION['message_success'] = 'Quantité modifiée avec succès !';
                                
                                header("Location: /");
                                exit();
                            }
                        }
                        
                        if(isset($_SESSION['message_success'])){
                            echo '<div class="alert alert-success text-center">' . $_SESSION['message_success'] . '</div>';
                            unset($_SESSION['message_success']); // Supprimer le message après affichage
                        }
                    ?>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                    <a href="/Panier/" type="button" class="btn btn-dark">Valider</a>
                </div>
                </div>
            </div>
        </div>
    </div>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
