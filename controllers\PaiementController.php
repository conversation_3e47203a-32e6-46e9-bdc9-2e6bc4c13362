<?php
class PaiementController extends Controller {
    public static function index($params) {
        
        // Vérifie si l'utilisateur est connecté
        if (!isset($_SESSION['user'])) {
            header("Location: /Connexion/");
            exit();
        }

        // Si le formulaire est soumis
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validation des champs
            if (isset($_POST['payment_method']) && $_POST['payment_method'] !== "") {

                // Récupère l'id de l'utilisateur actuel
                $user = DBManagerClient::getClientByName2($_SESSION['user'])->getID();

                $vartemp = 0;
                if($_POST['payment_method'] == "paypal"){
                    $vartemp = 2;
                }
                if($_POST['payment_method'] == "card"){
                    $vartemp = 1;
                }
                // Créer la commande
                DBManagerCommande::InsertCommande(date('Y-m-d'), null, $vartemp, $user);
                // Créer les détails de la commande
                foreach (DBManagerPanier::getPanierByUser($user) as $produit) {
                    DBManagerDetailsCmd::InsertDetailsCmd(DBManagerCommande::getLastId(), $produit->getIdProduit(), $produit->getQte());
                }
                // Supprimer le panier du client après la vérification de la commande
                DBManagerPanier::DeletePanier($user);
                // enlever phppageadresse et phpsecuriteconnexion
                header("Location: /ConfirmationCommande/");
                exit();

            }
        }

        // Affiche la vue
        $view = ROOT."/view/pagepaiement.php";
        self::render($view, $params);
    }
}
?>
