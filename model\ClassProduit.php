<?php
// Définition de la classe produit

class Produit{

    private int $ID;
    private string $nom;
    private float $prixUnitaire;
    private int $code_categ;
    private string $image_url;
    private int $stock;
    private string $descp;
    private ?int $Vente_flash;
    private int $StockAlert;

    public function __construct($ID ,$nom, $prixUnitaire, $code_categ, $image_url, $stock, $descp, $Vente_flash, $StockAlert){
        $this->ID = $ID;
        $this->nom = $nom;
        $this->prixUnitaire = $prixUnitaire;
        $this->code_categ = $code_categ;
        $this->image_url = $image_url;
        $this->stock = $stock;
        $this->descp = $descp;
        $this->Vente_flash = $Vente_flash;
        $this->StockAlert = $StockAlert;
    }
    public function getID() { return $this->ID; }

    public function getNom() { return $this->nom; }

    public function getPrixUnit() { return $this->prixUnitaire; }

    public function getCodeCateg() { return $this->code_categ; }

    public function getImageUrl() { return $this->image_url; }

    public function getStock() { return $this->stock; }

    public function getDescp() { return $this->descp; }
    
    public function getVenteFlash() { return $this->Vente_flash; }

    public function getStockAlert() { return $this->StockAlert; }

    public function setNom($nom) { return $this->nom = $nom; }

    public function setPrixUnit($prix) { return $this->prixUnitaire = $prix; }

    public function setCodeCateg($categ) { return $this->code_categ = $categ; }

    public function setImageUrl($urlimage) { return $this->image_url = $urlimage; }

    public function setStock($s) { return $this->stock = $s; }

    public function setDescp($des) { return $this->descp = $des; }

    public function setVenteFlash($vf) { return $this->Vente_flash = $vf; }

    public function setStockAlert($sa) { return $this->StockAlert = $sa; }
}

?>