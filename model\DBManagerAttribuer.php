<?php
class DBManagerAttribuer {
    private static ?\PDO $cnx;
    private static array $Attribuers = array();
    private static Attribuer $attribuer;

    public function __construct(){
        self::$attribuer = new Attribuer(0, 0, "");
    }

    /**
     * Permet de récupérer des attribuers de par un id
     * 
     */
    public static function getAttribuerByIdCmd($idCmd) {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Initialiser un tableau pour stocker les attribuers
        self::$Attribuers = [];

        // Requête SQL
        $query = "SELECT RefCmd, Id_Statut_Cmd, Date_statut_Cmd FROM attribuer WHERE RefCmd = ?";

        // Préparer la requête
        $sql = self::$cnx->prepare($query);

        // Exécuter la requête
        $sql->execute([$idCmd]);

        // Récupérer les attribuers
        while ($row = $sql->fetch(PDO::FETCH_ASSOC)) {
            self::$attribuer = new Attribuer($row['RefCmd'], $row['Id_Statut_Cmd'], $row['Date_statut_Cmd']);
            self::$Attribuers[] = self::$attribuer;
        }

        return self::$Attribuers;
    }

    /**
     * Permet de récupérer un attribuer récent de par un id
     * 
     */
    public static function getAttribuerByIdCmdRecent($idCmd) {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "SELECT RefCmd, Id_Statut_Cmd, Date_statut_Cmd FROM attribuer WHERE RefCmd = ? ORDER BY Date_statut_Cmd DESC";

        // Préparer la requête
        $sql = self::$cnx->prepare($query);

        // Exécuter la requête
        $sql->execute([$idCmd]);

        // Récupérer l'attribuer
        $row = $sql->fetch();

        if ($row) {
            self::$attribuer = new Attribuer($row['RefCmd'], $row['Id_Statut_Cmd'], $row['Date_statut_Cmd']);
        }

        return self::$attribuer;
    }


}
?>