<?php
class ConnexionController extends Controller{


    public static function index($params){

        // Vérifie si formulaire soumis
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            if (isset($_POST['id']) && isset($_POST['password'])) {
                $Id = htmlspecialchars($_POST['id']);
                $pass = htmlspecialchars($_POST['password']);
        
                // Vérification si l'utilisateur est un client
                $resultIdClient = DBManagerClient::getClientByName($Id);
        
                if ($resultIdClient) {
                    // Si l'utilisateur est un client, on vérifie le mot de passe client
                    $resultPassClient = DBManagerClient::getClient_Pwd($Id, $pass);
                    if ($resultPassClient) {
                        $_SESSION['user'] = $Id;
                        header("Location: /"); // Redirige le client vers la page d'accueil
                        exit();
                    } else {
                        $params['error'] = '<p style="color:red"> Mot de passe incorrect * </p>';
                    }
                }
            } else {
                $params['error'] = '<p style="color:red"> Veuillez remplir tous les champs * </p>';
            }
        }
        // appelle la vue
        $view = ROOT."/view/Auth/Connexion.php";
        self::render($view, $params);
    }

}
?>