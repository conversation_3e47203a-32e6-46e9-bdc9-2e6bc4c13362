<!DOCTYPE html>
<html lang="fr-FR">
    <head>
        <title>CSGOSHOP</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <!-- inclus bootstrap via CDN = Content Delivery Network (réseau de distribution de contenu) -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <!-- inclus style.css -->
         <link href="<?php ROOT?>/view/CSS/pageSecuriteAdmin.css" rel="stylesheet">

    </head>
    <body class="custom-body">
        <!-- Wrapper principal -->
        <div class="page-wrapper">

            <!-- Titre -->
            <div class="container">
                <h1 class="mt-4">Espace Connexion et Sécurité</h1>

                <div class="col col-lg-3 mt-2">
                    <a href="/BO-Admin/Accueil/" class="text-dark underline-change">
                        <i class="fas fa-arrow-left me-2"></i> Retour à l'accueil
                    </a>
                </div>
            </div>

            <!-- paramètre connexion & Sécurité -->
            <div class="container">
                <form action="/BO-Admin/PageSecurite/" method="POST">
                    <div class="row">
                            <div class="col col-lg-3">
                            </div>

                            <div class="col shadow-sm border rounded-3">
                                <h1 class="text-center mt-4"><i class="fas fa-user-shield me-2"></i>Information de Connexion</h1>

                                <?php if(isset($params['html']) && !empty($params['html'])): ?>
                                    <div class="success-message mx-5">
                                        <?php echo $params['html']; ?> <!-- Affiche le succès de la modification -->
                                    </div>
                                <?php endif; ?>

                                <div class="mt-4 ps-5 pe-5">
                                    <label for="User" class="mb-2"><i class="fas fa-user me-2"></i><strong>Utilisateur :</strong></label>
                                    <input type="text" class="form-control" id="User" name="User" placeholder="Nom d'utilisateur" value="<?php echo $params['username']; ?>">
                                </div>

                                <div class="mt-4 ps-5 pe-5">
                                    <label for="Nom" class="mb-2"><i class="fas fa-id-card me-2"></i><strong>Nom :</strong></label>
                                    <input type="text" class="form-control" id="Nom" name="Nom" placeholder="Nom" value="<?php echo $params['nom']; ?>">
                                </div>

                                <div class="mt-4 ps-5 pe-5">
                                    <label for="Prenom" class="mb-2"><i class="fas fa-id-card me-2"></i><strong>Prénom :</strong></label>
                                    <input type="text" class="form-control" id="Prenom" name="Prenom" placeholder="Prénom" value="<?php echo $params['prenom']; ?>">
                                </div>

                                <div class="mt-4 ps-5 pe-5">
                                    <label for="email" class="mb-2"><i class="fas fa-envelope me-2"></i><strong>E-mail :</strong></label>
                                    <input type="email" class="form-control" id="email" name="email" placeholder="email" value="<?php echo $params['email']; ?>">
                                </div>

                                <div class="mt-4 ps-5 pe-5">
                                    <label for="mobile" class="mb-2"><i class="fas fa-mobile-alt me-2"></i><strong>Mobile :</strong></label>
                                    <input type="text" class="form-control" id="mobile" name="mobile" placeholder="Tel" value="<?php echo $params['mobile']; ?>">
                                </div>

                                <div class="mt-4 ps-5 pe-5">
                                    <label for="password" class="mb-2"><i class="fas fa-lock me-2"></i><strong>Mot de passe :</strong></label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="password" placeholder="Mot de passe" value="<?php echo $params['password']; ?>" disabled>
                                    </div>
                                    <button type="button" class="btn btn-outline-dark mt-3 w-100" data-bs-toggle="modal" data-bs-target="#Modal">
                                        <i class="fas fa-key me-2"></i>Modifier mot de passe
                                    </button>
                                </div>

                                <div class="text-center mt-5 mb-4">
                                    <button class="btn btn-outline-dark px-5" type="submit" id="connexion">
                                        <i class="fas fa-save me-2"></i>Enregistrer les modifications
                                    </button>
                                </div>
                            </div>

                            <div class="col col-lg-3">
                            </div>
                    </div>
                </form>
            </div>

            <!-- MODAL PASSWORD -->
            <div class="modal fade" id="Modal" tabindex="-1" aria-labelledby="modalLabel" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="modalLabel"><i class="fas fa-key me-2"></i>Modifier le mot de passe</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <?php
                            $htmlpass ='';

                            $htmlform = '<form action="#" method="POST" class="password-form">
                                <div class="mb-3">
                                    <label for="mdp" class="form-label"><i class="fas fa-lock me-2"></i><strong>Mot de passe actuel : </strong></label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="mdp" name="mdp" placeholder="Entrez votre mot de passe actuel">
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword(\'mdp\')">
                                            <i class="fas fa-eye" id="mdp-icon"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-outline-dark">
                                        <i class="fas fa-check-circle me-2"></i>Valider
                                    </button>
                                </div>
                            </form>';

                            if(isset($_SESSION['User'])){
                                if(isset($_POST['mdp'])){
                                    $inputmdp = htmlspecialchars($_POST['mdp']);

                                    if(DBManagerAdmin::getAdmin_Pwd($_SESSION['User'], $inputmdp) == false){
                                        $htmlpass .= '<div class="error-message mb-3"><i class="fas fa-exclamation-circle me-2"></i>Mot de passe incorrect</div>';
                                    }
                                    else{
                                        $htmlpass .= '';
                                        $htmlform = '';
                                        echo '<form name="form_mdp" action="#" method="POST" class="password-form">
                                            <div class="mb-3">
                                                <label for="mdp2" class="form-label"><i class="fas fa-lock me-2"></i><strong>Nouveau mot de passe : </strong></label>
                                                <div class="input-group">
                                                    <input type="password" class="form-control" id="mdp2" name="mdp2" placeholder="Entrez votre nouveau mot de passe">
                                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword(\'mdp2\')">
                                                        <i class="fas fa-eye" id="mdp2-icon"></i>
                                                    </button>
                                                </div>
                                                <div class="form-text">Votre mot de passe doit contenir au moins 8 caractères.</div>
                                            </div>

                                            <div class="mb-3">
                                                <label for="mdpconfirmation" class="form-label"><i class="fas fa-lock me-2"></i><strong>Confirmation : </strong></label>
                                                <div class="input-group">
                                                    <input type="password" class="form-control" id="mdpconfirmation" name="mdpconfirmation" placeholder="Confirmez votre nouveau mot de passe">
                                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword(\'mdpconfirmation\')">
                                                        <i class="fas fa-eye" id="mdpconfirmation-icon"></i>
                                                    </button>
                                                </div>
                                            </div>

                                            <div class="d-grid gap-2">
                                                <button type="button" class="btn btn-outline-dark" onclick="submit_form()">
                                                    <i class="fas fa-save me-2"></i>Enregistrer
                                                </button>
                                            </div>
                                        </form>';
                                    }
                                }
                            }
                            echo $htmlpass; // Affiche l'erreur si le mot de passe est incorrect.
                            echo $htmlform;
                            ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chargement javascript -->
            <!-- js pour bootstrap -->
            <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script>
            <!-- js text file -->
            <script src="<?php ROOT?>/Js/formMdp.js"></script>

        </div>
    </body>
</html>
