<?php
class PagePanierController extends Controller{


    public static function index($params){

        // Vérifie si l'utilisateur est connecté en tant qu'user
        if (!isset($_SESSION['user'])) {
            // Redirige vers la page de connexion si non connecté
            header("Location: /Connexion/");
            exit();
        }

        // Vérifie si l'utilisateur a appuyé sur le bouton pour supprimer un produit du panier
        if(isset($_GET['SupprimerPanier'])){
            DBManagerPanier::SupprimerProduitPanier(DBManagerClient::getClientByName2($_SESSION['user'])->getID(), $_GET['SupprimerPanier']);
        }

        // Vérifie si l'utilisateur a appuyé sur le bouton pour modifier la quantité d'un produit dans le panier
        if(isset($_GET['BtnMoins'])){
            DBManagerPanier::ModifierQtePanier(DBManagerClient::getClientByName2($_SESSION['user'])->getID(), $_GET['BtnMoins'], $_GET['ProduitPanierQTE'] - 1);
        }

        if(isset($_GET['BtnPlus'])){
            DBManagerPanier::ModifierQtePanier(DBManagerClient::getClientByName2($_SESSION['user'])->getID(), $_GET['BtnPlus'], $_GET['ProduitPanierQTE'] + 1);
        }

        // appelle la vue
        $view = ROOT."/view/pagepanier.php";
        self::render($view, $params);
    }

}
?>