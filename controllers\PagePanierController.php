<?php
class PagePanierController extends Controller{


    public static function index($params){

        // Vérifie si l'utilisateur est connecté en tant qu'user
        if (!isset($_SESSION['user'])) {
            // Redirige vers la page de connexion si non connecté
            header("Location: /Connexion/");
            exit();
        }

        // Vérifie si l'utilisateur a appuyé sur le bouton pour supprimer un produit du panier
        if(isset($_GET['SupprimerPanier'])){
            var_dump($_GET['SupprimerPanier']);
            DBManagerPanier::SupprimerProduitPanier(DBManagerClient::getClientByName2($_SESSION['user'])->getID(), $_GET['SupprimerPanier']);
        }
        
        // appelle la vue
        $view = ROOT."/view/pagepanier.php";
        self::render($view, $params);
    }

}
?>