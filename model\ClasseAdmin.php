<?php
// Définition de la classe Admin

class Admin{

    private string $ID;
    private string $nom;
    private string $prenom;
    private string $Mdp;
    private ?string $tel;
    private string $email;
    private string $User;


    public function __construct($ID, $User, $nom, $prenom, $Mdp, $tel, $email){
        $this->ID = $ID;
        $this->nom = $nom;
        $this->prenom = $prenom;
        $this->Mdp = $Mdp;
        $this->tel = $tel;
        $this->email = $email;
        $this->User = $User;

    }

    public function getID() { return $this->ID; }

    public function getNom() { return $this->nom; }

    public function getPrenom() { return $this->prenom; }

    public function getMdp() { return $this->Mdp; }

    public function getTel() { return $this->tel; }

    public function getEmail() { return $this->email; }

    public function getUtilisateur() { return $this->User; }

    public function setNom($nomsaisie) {return $this->nom = $nomsaisie; }

    public function setPrenom($prenomsaisie) { return $this->prenom = $prenomsaisie; }

    public function setMdp($Mdpsaisie) { return $this->Mdp = $Mdpsaisie; }

    public function setTel($mobilesaisie) { return $this->tel = $mobilesaisie; }

    public function setEmail($emailsaisie) { return $this->email = $emailsaisie; }

    public function setUtilisateur($usersaisie) { return $this->User = $usersaisie; }
}

?>