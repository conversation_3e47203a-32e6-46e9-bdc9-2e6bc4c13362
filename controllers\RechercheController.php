<?php
class RechercheController extends Controller{


    public static function index($params) {
        // appelle la vue
        $view = ROOT."/view/Recherche.php";
        self::render($view, $params);
    }

    /**
     * Action qui rafraîchit les produits recherchés
     * params : tableau des paramètres
     */
    public static function recherche($params) {
        if (isset($_GET['Mots']) && !empty(trim($_GET['Mots']))) {
            // Rediriger vers l'action index avec le terme de recherche
            header("Location: /Recherche/". urlencode($_GET['Mots']) ."/");
            exit();
        }
        
        // Si pas de terme de recherche, rediriger vers l'accueil
        header("Location: /");
        exit();
    }
}
?>
