/* Le fond de couleur du site */
.custom-body{
    background-color: #e2e1ef !important;
}

/* Barre de navigation, couleur du texte qui change */

.item-link{
  color: #c0c0c0;
}

.item-link:hover{
  color: rgb(255, 255, 255);
}

.background {
background-color: transparent;
background-image: linear-gradient(65deg, #383F51, #1d0feb);
}

.nav-bloc a{
padding: 0px 16px;
}

.nav-bloc li a:hover {
box-shadow: 0px 3px 0px 0px #c9c9c9ef; /* Ombre noire en dessous du texte */
outline: 1px solid transparent;
border-radius: 1px;
transition: box-shadow 0.3s ease; /* Transition pour l'animation fluide */
}

/* Les cards */
.card {
  width: 300px;
  height: 254px;
  border-radius: 30px;
  background: #e0e0e0;
  box-shadow: 15px 15px 30px #bebebe,
             -15px -15px 30px #ffffff;
  transition: all 0.1s ease;

}

.card:hover {
  background: #d5d5d5;
  box-shadow: 15px 15px 30px #d5d5d5,
             -15px -15px 30px #d5d5d5;
  transition: all 0.1s ease;
}

.card p,
.fs-3{
  color: #000000;
  text-decoration: none !important;
}

/* Customisation du rond */
.round {
  width: 70px;
  height: 70px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(80, 79, 79);
  border-radius: 50%;
  cursor: pointer;
  transition-duration: .3s;
  box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.13);
  border: none;
}

.round:hover {
  background-color: rgb(84, 84, 84);
}

/* Footer */
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
}

.page-wrapper {
  min-height: 100vh; /* Changé de 100% à 100vh */
  display: flex;     /* Ajouté */
  flex-direction: column; /* Ajouté */
}

.content {
  flex: 1;          /* Ajouté */
  width: 100%;
}

footer {
  margin-top: auto; /* Ajouté */
  width: 100%;
  background-color: #000000;
  z-index: 1000;
}

/* Ajustements pour le wrapper des icônes sociales */
.wrapper {
    padding-top: 20px;
    padding-bottom: 20px;
    margin-bottom: 0;
}

.wrapper {
    display: inline-flex;
    list-style: none;
    height: 120px;
    width: 100%;
    padding-top: 40px;
    font-family: "Poppins", sans-serif;
    justify-content: center;
  }
  
  .wrapper .icon {
    position: relative;
    background: #fff;
    border-radius: 50%;
    margin: 10px;
    width: 50px;
    height: 50px;
    font-size: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    box-shadow: 0 10px 10px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }
  
  .wrapper .tooltip {
    position: absolute;
    top: 0;
    font-size: 14px;
    background: #fff;
    color: #fff;
    padding: 5px 8px;
    border-radius: 5px;
    box-shadow: 0 10px 10px rgba(0, 0, 0, 0.1);
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }
  
  .wrapper .tooltip::before {
    position: absolute;
    content: "";
    height: 8px;
    width: 8px;
    background: #fff;
    bottom: -3px;
    left: 50%;
    transform: translate(-50%) rotate(45deg);
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }
  
  .wrapper .icon:hover .tooltip {
    top: -45px;
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
  }
  
  .wrapper .icon:hover span,
  .wrapper .icon:hover .tooltip {
    text-shadow: 0px -1px 0px rgba(0, 0, 0, 0.1);
  }
  
  .wrapper .facebook:hover,
  .wrapper .facebook:hover .tooltip,
  .wrapper .facebook:hover .tooltip::before {
    background: #1877F2;
    color: #fff;
  }
  
  a .mail{
    color: #000000;
  }
  .wrapper .mail:hover,
  .wrapper .mail:hover .tooltip,
  .wrapper .mail:hover .tooltip::before {
    background: #1DA1F2;
    color: #fff;
  }
  
  a .instagram{
    color: #E4405F;
  }
  .wrapper .instagram:hover,
  .wrapper .instagram:hover .tooltip,
  .wrapper .instagram:hover .tooltip::before {
    background: #E4405F;
    color: #fff;
  }
  
/* Footer background */

/* TABLEAU  */
.tablep {
    width: 100%;
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin: 2rem 0;
}

.tablep thead {
    background-image: linear-gradient(65deg, #383F51, #1d0feb);
}

.tablep th {
    padding: 1.2rem 1.5rem;
    color: white !important;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
    border: none;
}

.tablep td {
    padding: 1.2rem 1.5rem;
    border: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    color: #333;
}

.tablep tbody tr {
    transition: all 0.3s ease;
}

.tablep tbody tr:hover {
    background-color: rgba(29, 15, 235, 0.05);
    transform: translateY(-2px);
}

.tablep .fw-medium {
    color: #1d0feb;
    font-weight: 600;
}

/* FOOTER FIXE DANS LE MÊME STYLE */
html, body {
    height: 100%;
    margin: 0;
}

body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.page-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.content {
    flex: 1;
}

footer {
    background-image: linear-gradient(65deg, #383F51, #1d0feb);
    margin-top: auto;
    width: 100%;
}

/* Animation du tableau */
.tablep {
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .tablep {
        font-size: 0.9rem;
    }
    
    .tablep th, 
    .tablep td {
        padding: 1rem;
    }
}
