<?php
class DBManagerPanier {
    private static ?\PDO $cnx;
    private static array $Paniers = array();
    private static Panier $Panier;

    public function __construct(){
        self::$Panier = new Panier(0,0,0);
    }

    /**
     * Permet de récupérer le panier d'un client
     * 
     */
    public static function getPanierByUser($ID) {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "SELECT idClient, idProduit, qte FROM Panier Where idClient = ?";

        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);

        // Exécuter la requête
        $sql1->execute([$ID]);

        // Récupère les produits du client dans son panier
        while ($row = $sql1->fetch(PDO::FETCH_ASSOC)) {
            self::$Panier = new Panier($row['idClient'], $row['idProduit'], $row['qte']);
            self::$Paniers[] = self::$Panier;
        }

        return self::$Paniers;
    }

    /**
     * Vérifie si le panier du client est vide
     * 
     */
    public static function VerifiePanier($ID) {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "SELECT idClient, idProduit, qte FROM Panier Where idClient = ?";

        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);

        // Exécuter la requête
        $sql1->execute([$ID]);

        // Vérifie si le panier est vide
        $result = $sql1->fetchColumn();

        return $result;
    }

    /**
     * Permet d'ajouter un produit dans le panier d'un client
     * 
     */
    public static function AjouterProduitPanier($idClient, $idProduit, $qte){
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "INSERT INTO Panier(idClient, idProduit, qte) VALUES(?, ?, ?)";
        
        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);
        
        // Exécuter la requête
        $produit = $sql1->execute([$idClient, $idProduit, $qte]);
    }

    /**
     * Permet de modifier la quantité d'un produit dans le panier d'un client
     * 
     */
    public static function ModifierQtePanier($idClient, $idProduit, $nouvelleQuantite) {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();
        
        // Requête SQL
        $query = "UPDATE Panier SET qte = ? WHERE idClient = ? AND idProduit = ?";
        
        // Préparer la requête
        $sql = self::$cnx->prepare($query);
        
        // Exécuter la requête
        return $sql->execute([$nouvelleQuantite, $idClient, $idProduit]);
    }
    
    /**
     * Permet de supprimer un produit dans le panier d'un client
     * 
     */
    public static function SupprimerProduitPanier($idClient, $idProduit) {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();
        
        // Requête SQL
        $query = "DELETE FROM Panier WHERE idClient = ? AND idProduit = ?";
        
        // Préparer la requête
        $sql = self::$cnx->prepare($query);
        
        // Exécuter la requête
        return $sql->execute([$idClient, $idProduit]);
    }
    
    /**
     * Permet de supprimer le panier d'un client
     * 
     */
    public static function DeletePanier($idClient) {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();
        
        // Requête SQL
        $query = "DELETE FROM Panier WHERE idClient = ?";
        
        // Préparer la requête
        $sql = self::$cnx->prepare($query);
        
        // Exécuter la requête
        return $sql->execute([$idClient]);
    }

}
?>
