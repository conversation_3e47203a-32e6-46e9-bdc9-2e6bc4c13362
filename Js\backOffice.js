/* fonction de suppression d'un produit */
function confirmDelete(nomProduit) {
    if (confirm("Cliquer sur ce bouton va supprimer définitivement le produit '" + nomProduit + "'")) {

        // On créer un formulaire pour par la suite le soumettre et que le code php fonctionne
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = ''; // l'action est la même page

        var input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'Delete';
        input.value = nomProduit;

        // Ajoute au form le bouton de suppression et si l'admin clique alors le form est envoyer ;)
        form.appendChild(input);
        document.body.appendChild(form);
        form.submit();
    }
}