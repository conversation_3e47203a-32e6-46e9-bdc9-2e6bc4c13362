/* Dans la modification du mode de livraison on vérifie si les champs sont remplis*/
document.getElementById('button_paiement').addEventListener('click', function(e) {
    var nom = document.getElementById('nom').value;
    var prenom = document.getElementById('prenom').value;
    var adresse = document.getElementById('adresse').value;
    var cp = document.getElementById('cp').value;
    var ville = document.getElementById('ville').value;
    var pays = document.getElementById('pays').value;

    if(nom == "" || prenom == "" || adresse == "" || cp == "" || ville == "" || pays == "") {
        e.preventDefault(); /* Empêche la redirection */
        alert("Les champs doivent être remplis !");
    }
    else{
        document.forms['form_livraison'].submit()
    }
});
