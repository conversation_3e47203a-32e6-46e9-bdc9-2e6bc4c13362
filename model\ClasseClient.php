<?php
// Définition de la classe Client

class Client{

    private string $ID;
    private string $nom;
    private string $prenom;
    private string $Mdp;
    private ?string $tel;
    private string $email;
    private string $User;
    private ?string $Ad_p;
    private ?string $code_p;
    private ?string $ville;
    private ?string $pays;

    public function __construct($ID, $User, $nom, $prenom, $Mdp, $tel, $email, $Ad_p, $code_p, $ville, $pays){
        $this->ID = $ID;
        $this->nom = $nom;
        $this->prenom = $prenom;
        $this->Mdp = $Mdp;
        $this->tel = $tel;
        $this->email = $email;
        $this->User = $User;
        $this->Ad_p = $Ad_p;
        $this->code_p = $code_p;
        $this->ville = $ville;
        $this->pays = $pays;
    }

    public function getID() { return $this->ID; }

    public function getNom() { return $this->nom; }

    public function getPrenom() { return $this->prenom; }

    public function getMdp() { return $this->Mdp; }

    public function getTel() { return $this->tel; }

    public function getEmail() { return $this->email; }

    public function getUtilisateur() { return $this->User; }

    public function getAdresse_postale() { return $this->Ad_p; }

    public function getCode_postal() { return $this->code_p; }

    public function getVille() { return $this->ville; }

    public function getPays() { return $this->pays; }

    public function setNom($nomsaisie) {return $this->nom = $nomsaisie; }

    public function setPrenom($prenomsaisie) { return $this->prenom = $prenomsaisie; }

    public function setMdp($Mdpsaisie) { return $this->Mdp = $Mdpsaisie; }

    public function setTel($mobilesaisie) { return $this->tel = $mobilesaisie; }

    public function setEmail($emailsaisie) { return $this->email = $emailsaisie; }

    public function setUtilisateur($usersaisie) { return $this->User = $usersaisie; }

    public function setAdresse_postale($adressesaisie) { return $this->Ad_p = $adressesaisie; }

    public function setCode_Postale($code_psaisie) { return $this->code_p = $code_psaisie; }

    public function setVille($villesaisie) { return $this->ville = $villesaisie; }

    public function setPays($payssaisie) { return $this->pays = $payssaisie; }
}

?>