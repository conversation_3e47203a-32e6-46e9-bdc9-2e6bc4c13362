<?php
// Définition de la classe Panier

class Panier{
    private int $idClient;
    private int $idProduit;
    private int $qte;

    public function __construct($idClient, $idProduit, $qte){
        $this->idClient = $idClient;
        $this->idProduit = $idProduit;
        $this->qte = $qte;
    }

    public function getIdClient() { return $this->idClient; }

    public function getIdProduit() { return $this->idProduit; }

    public function getQte() { return $this->qte; }

    public function setQte($qte) { return $this->qte = $qte; }
}

?>