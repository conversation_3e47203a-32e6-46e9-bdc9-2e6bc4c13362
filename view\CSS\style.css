/* Footer fixe en bas de page */
html, body {
    height: 100%;
    margin: 0;
    padding: 0;
}

.page-wrapper {
    min-height: 100vh; /* <PERSON><PERSON> de 100% à 100vh */
    display: flex;     /* <PERSON><PERSON><PERSON> */
    flex-direction: column; /* A<PERSON>té */
}

.content {
    flex: 1;          /* Ajouté */
    width: 100%;
}

footer {
    margin-top: auto; /* Ajouté */
    width: 100%;
    background-color: #000000;
    z-index: 1000;
}

/* Ajustements pour le wrapper des icônes sociales */
.wrapper {
    padding-top: 20px;
    padding-bottom: 20px;
}

/* Le fond de couleur du site */
.custom-body{
    background-color: #e2e1ef !important;
}

/* Barre de navigation, background ,couleur du texte qui change */

.item-link{
    color: #c0c0c0;
}

.item-link:hover{
    color: rgb(255, 255, 255);
}

.background {
  background-color: transparent;
  background-image: linear-gradient(65deg, #383F51, #1d0feb);
}

.nav-bloc a{
  padding: 0px 16px;
}

.nav-bloc li a:hover {
  box-shadow: 0px 3px 0px 0px #c9c9c9ef; /* Ombre noire en dessous du texte */
  outline: 1px solid transparent;
  border-radius: 1px;
  transition: box-shadow 0.3s ease; /* Transition pour l'animation fluide */
}

/* Personnalisation du tableau */
.tablep thead tr th {
  font-size: 18px !important;
  color: #fff !important;
  line-height: 1.4 !important;
  background-color: #6c7ae0 !important;
  padding: 20px;
}

.tablep{
  border-collapse: collapse;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 0 40px 0 rgba(0, 0, 0, .15);

}


.tablep :nth-child(even){
  background-color: #ffffff;
}

.tablep thead tr th:nth-child(1),
.tablep tbody tr td:nth-child(1){
  padding-left: 40px;
}

.tablep tbody tr td{
  padding-top: 16px;
  padding-bottom: 16px;
  border-style: none !important;
}

/* Personnalisation d'une box */

.box-perso{
  border-radius: 10px;
  background-color: #fff;
  width: 100%;
  box-shadow: 0 0 40px 0 rgba(0, 0, 0, .15);
  padding: 20px;
}


/* Ombre des cartes de produits */

.shadow-card{
    box-shadow: 3px 5px 5px rgba(0, 0, 0, 0.327);
    border-radius: 20px;
}


/* Bordure en dégradé */
.border-gradient {
    border-image: radial-gradient(at center, #000000 60%, #e2e1ef 80%);
    border-bottom: 10px solid transparent;
    border-image-slice: 1;
}

.border-carousel{
    border-bottom: 20px solid #000000;
}

/* Customisation des filtres */

.custom-form .form-check-input:checked{
  background-color: #202020 !important;
  border-color: #202020 !important;
}

.custom-form .form-check-input:hover{
  background-color: #757575 !important;
  border-color: #757575 !important;
  transition: all 0.1s ease;
}

.custom-form .form-check-input:focus{
  box-shadow: 0 0 0 .25rem #b1b1b177 !important;
  transition: all 0.1s ease;
}

.custom-form-checkbox .form-check-input[type=checkbox]{
  border-radius: 100px;
}

.custom-form-checkbox .form-check-input[type=checkbox]:checked{
  background-color: #202020 !important;
  border-color: #202020 !important;
}

.custom-form-checkbox .form-check-input:hover{
  background-color: #757575 !important;
  border-color: #757575 !important;
  transition: all 0.1s ease;
}

.custom-form-checkbox .form-check-input:focus{
  box-shadow: 0 0 0 .25rem #b1b1b177 !important;
  transition: all 0.1s ease;
}


/* Footer, réseau sociaux */

.wrapper {
    display: inline-flex;
    list-style: none;
    height: 120px;
    width: 100%;
    padding-top: 40px;
    font-family: "Poppins", sans-serif;
    justify-content: center;
  }
  
  .wrapper .icon {
    position: relative;
    background: #fff;
    border-radius: 50%;
    margin: 10px;
    width: 50px;
    height: 50px;
    font-size: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    box-shadow: 0 10px 10px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }
  
  .wrapper .tooltip {
    position: absolute;
    top: 0;
    font-size: 14px;
    background: #fff;
    color: #fff;
    padding: 5px 8px;
    border-radius: 5px;
    box-shadow: 0 10px 10px rgba(0, 0, 0, 0.1);
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }
  
  .wrapper .tooltip::before {
    position: absolute;
    content: "";
    height: 8px;
    width: 8px;
    background: #fff;
    bottom: -3px;
    left: 50%;
    transform: translate(-50%) rotate(45deg);
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }
  
  .wrapper .icon:hover .tooltip {
    top: -45px;
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
  }
  
  .wrapper .icon:hover span,
  .wrapper .icon:hover .tooltip {
    text-shadow: 0px -1px 0px rgba(0, 0, 0, 0.1);
  }
  
  .wrapper .facebook:hover,
  .wrapper .facebook:hover .tooltip,
  .wrapper .facebook:hover .tooltip::before {
    background: #1877F2;
    color: #fff;
  }
  
  a .mail{
    color: #000000;
  }
  .wrapper .mail:hover,
  .wrapper .mail:hover .tooltip,
  .wrapper .mail:hover .tooltip::before {
    background: #1DA1F2;
    color: #fff;
  }
  
  a .instagram{
    color: #E4405F;
  }
  .wrapper .instagram:hover,
  .wrapper .instagram:hover .tooltip,
  .wrapper .instagram:hover .tooltip::before {
    background: #E4405F;
    color: #fff;
  }
  
/* Footer background */

footer{
    background-color: #000000;
    width: 100%;
}

/* Style du bouton retour */
.btn-return {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    background: linear-gradient(145deg, #0984e3, #74b9ff);
    color: white;
    border-radius: 8px;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(9, 132, 227, 0.2);
}

.btn-return:hover {
    background: linear-gradient(145deg, #0984e3, #6c5ce7);
    transform: translateX(-5px);
    color: white;
    box-shadow: 0 4px 8px rgba(9, 132, 227, 0.3);
}

.btn-return svg {
    transition: transform 0.3s ease;
}

.btn-return:hover svg {
    transform: translateX(-3px);
}

/* Style du carousel */
#carouselExampleDark {
  margin: 0;
  width: 100%;
  position: relative;
}

#carouselExampleDark .carousel-inner {
  width: 100%;
  height: 600px; /* Hauteur plus grande pour un meilleur impact visuel */
}

#carouselExampleDark .carousel-item {
  width: 100%;
  height: 100%;
}

#carouselExampleDark .carousel-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.85); /* Assombrit légèrement l'image pour une meilleure lisibilité du texte */
  transition: transform 0.5s ease;
}

#carouselExampleDark .carousel-item:hover img {
  transform: scale(1.02);
}

#carouselExampleDark .carousel-caption {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  border-radius: 10px;
  padding: 20px 30px;
  max-width: 800px;
  margin: 0 auto;
  left: 50%;
  transform: translateX(-50%);
  bottom: 50px;
}

#carouselExampleDark .carousel-caption h5 {
  color: #fff !important;
  font-size: 2rem;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  margin-bottom: 10px;
}

#carouselExampleDark .carousel-caption p {
  color: #fff !important;
  font-size: 1.2rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  margin-bottom: 0;
}

#carouselExampleDark .carousel-control-prev,
#carouselExampleDark .carousel-control-next {
  width: 10%;
  opacity: 0.8;
}

#carouselExampleDark .carousel-control-prev-icon,
#carouselExampleDark .carousel-control-next-icon {
  filter: invert(1) grayscale(100);
  width: 3rem;
  height: 3rem;
}

#carouselExampleDark .carousel-indicators {
  bottom: 20px;
}

#carouselExampleDark .carousel-indicators button {
  background-color: #fff;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  margin: 0 6px;
  border: none;
}

#carouselExampleDark .carousel-indicators button.active {
  background-color: #1d0feb;
  transform: scale(1.2);
}

.border-carousel {
  border-bottom: 20px solid #000000;
}
