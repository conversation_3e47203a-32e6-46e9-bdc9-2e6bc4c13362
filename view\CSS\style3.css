/* Le fond de couleur du site */
body{
  background-color: #5834931a !important;
}

/* Effet de text souligné apparaissant en passant la souris sur le text */

.underline-change,
.underline-change2{
  text-decoration: none; /* Enlève le text souligné */
}

/* Le bouton retour */
.underline-change::after{
  content: '';
  position: absolute;
  width: 80px;
  height: 3px;
  background: rgb(0, 0, 0);
  bottom: 90%;
  left: 7.5%;
  border-radius: 40px;
  transform-origin: right;
  transform: scaleX(0);
  transition: transform .3s;
}

.underline-change:hover:after{
  transform-origin: left;
  transform: scaleX(1);
}

/* Le bouton retour */

.underline-change2::after{
  content: '';
  position: absolute;
  width: 180px;
  height: 3px;
  background: rgb(0, 0, 0);
  bottom: 20%;
  left: 43%;
  border-radius: 40px;
  transform-origin: right;
  transform: scaleX(0);
  transition: transform .3s;
}

.underline-change2:hover:after{
  transform-origin: left;
  transform: scaleX(1);
}