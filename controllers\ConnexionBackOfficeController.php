<?php
class ConnexionBackOfficeController extends Controller {
    public static function index($params) {

        // Vérifie le formulaire
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            if (isset($_POST['username']) && isset($_POST['password'])) {
                $username = htmlspecialchars($_POST['username']);
                $password = htmlspecialchars($_POST['password']);
                
                // Vérifie la connexion de l'administrateur
                if (DBManagerAdmin::getAdminByName($username)) {
                    if (DBManagerAdmin::getAdmin_Pwd($username, $password)) {
                        $_SESSION['User'] = $username; // Stocke le nom d'utilisateur dans une autre session (pour évitér les conflits avec la session utilisateur)
                        header("Location: /BO-Admin/Accueil/");
                        exit();
                    } else {
                        $params['error'] = "Mot de passe incorrect";
                    }
                } else {
                    $params['error'] = "Identifiant incorrect";
                }
            }
        }

        // Affiche la vue
        $view = ROOT."/view/BackOffice/ConnexionBackOffice.php";
        self::render($view, $params);
    }

}
?>
