<?php
// Définition de la classe Attribuer

class Attribuer{

    private string $RefCmd;
    private string $Id_Statut_Cmd;
    private string $Date_statut_Cmd;

    public function __construct($RefCmd, $Id_Statut_Cmd, $Date_statut_Cmd){
        $this->RefCmd = $RefCmd;
        $this->Id_Statut_Cmd = $Id_Statut_Cmd;
        $this->Date_statut_Cmd = $Date_statut_Cmd;

    }

    public function getRefCmd() { return $this->RefCmd; }

    public function getIdStatutCmd() { return $this->Id_Statut_Cmd; }

    public function getDateStatutCmd() { return $this->Date_statut_Cmd; }
    
    public function setDateStatutCmd($Date_statut_Cmd) { return $this->Date_statut_Cmd = $Date_statut_Cmd; }
}

?>