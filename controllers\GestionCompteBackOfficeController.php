<?php
class GestionCompteBackOfficeController extends Controller {

    public static function index($params) {
        $params['html'] = '';

        // Vérifie si l'utilisateur est connecté en tant qu'admin
        if (!isset($_SESSION['User'])) {
            // Redirige vers la page de connexion si non connecté
            header("Location: /BO-Admin/");
            exit();
        }

        if(isset($_POST['GestionPrivileges'])){
            $privileges = [];
            if(isset($_POST['readCheck'])){
                $privileges[] = 'SELECT';
            }
            if(isset($_POST['updateCheck'])){
                $privileges[] = 'UPDATE';
            }
            if(isset($_POST['createCheck'])){
                $privileges[] = 'CREATE';
            }
            if(isset($_POST['deleteCheck'])){
                $privileges[] = 'DELETE';
            }
            DBManagerAdmindb::updatePrivilegesAccountAdmins($_POST['AdminSelect'], 'localhost', $privileges);

            $params['html'] = '<p class="text-center" style="color: green;">Modification effectuée avec succès !</p>';
        }

        // Affiche la vue
        $view = ROOT."/view/BackOffice/gestioncompte.php";
        self::render($view, $params);
    }

    public static function ajaxPrivileges($params) {
        // Traitement des requêtes AJAX
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['adminName'])) {
            
            $adminName = htmlspecialchars($_POST['adminName']);

            // Récupération des privilèges de l'admin
            $privileges = DBManagerAdmindb::getPrivilegesAccountAdmins($adminName, 'localhost');

            echo json_encode($privileges);
        }
    }

}
?>
