<?php
class DBManagerCategorie {
    private static ?\PDO $cnx;
    private static array $Categories = array();
    private static Categorie $Categorie;

    public function __construct(){
        self::$Categorie = new Categorie(0,'');
    }

    public static function getLesCategories() {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Initialiser un tableau pour stocker les Categories
        self::$Categories = [];

        // Requête SQL
        $query = "SELECT code_categ, nom_categ FROM Categorie";

        // Préparer la requête
        $sql = self::$cnx->prepare($query);

        // Exécuter la requête
        $sql->execute();

        // Récupérer les Categories
        while ($row = $sql->fetch(PDO::FETCH_ASSOC)) {
            self::$Categorie = new Categorie($row['code_categ'], $row['nom_categ']);
            self::$Categories[] = self::$Categorie;
        }

        return self::$Categories;
    }

}
?>