<!DOCTYPE html>
<html lang="fr-FR">
    <head>
        <title>CSGOSHOP</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <!-- inclus bootstrap via CDN = Content Delivery Network (réseau de distribution de contenu) -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <!-- inclus style.css -->
        <link href="<?php ROOT?>/view/CSS/style.css" rel="stylesheet">
        <link href="<?php ROOT?>/view/CSS/panier.css" rel="stylesheet">

    </head>
    <body class="custom-body">
        <div class="page-wrapper">
            <!-- Navbar -->
            <?php include_once ROOT.'/view/template/navbar.php'; ?>

            <form action="#" method="GET" class="content">
                <!-- Carte des produits à la une ! -->
                <div class="container mt-5">
                    <h1>Vos recherches :</h1>

                    <div id="liste-produits" class="row text-center mt-2 content">

                        <?php
                            //Nettoyage de la recherche
                            $larecherche = htmlspecialchars($_GET['Mots']);

                            // récupère les produits de par les filtres
                            $produits = DBManagerProduits::getProduitsBySearch($larecherche);
                            
                            // Pour chaque produits on les affiches dans le HTML
                            $html = '';
                            foreach($produits as $produit){

                                // On vérifie si le produit qu'on recherche et bien dans la liste si oui alors on ajoute une réduction ou autres...
                                $addhtml = '';

                                if(DBManagerProduits::getProduitSelect("Gut Fade")->getNom() == $produit->getNom()){
                                    $addhtml = '<span class="badge bg-danger" style="width:100%">PRIX RÉDUIT 📉 : -20% sur ce produit !</span>';
                                }

                                if(DBManagerProduits::getProduitSelect("Stiletto Fade")->getNom() == $produit->getNom()){
                                    $addhtml = '<span class="badge bg-danger" style="width:100%">💥PRIX CHOC : -55% sur ce produit !🔥</span>';
                                }

                                if(DBManagerProduits::getProduitSelect("Karambit Blade")->getNom() == $produit->getNom()){
                                    $addhtml = '<span class="badge bg-dark ms-5" style="width:60%">❌ Hors stock ❌</span>';
                                    $html .= '                <div class="card shadow-card me-4 mt-5" style="width: 18rem;">'. $addhtml .'
                                    <img src="/public'. $produit->getImageUrl() .'" class="card-img-top" alt="...">
                                    <div class="card-body">
                                        <h5 class="card-title">'. $produit->getNom() .'</h5>
                                        <p class="card-text text-center fs-4 fw-lighter text-decoration-line-through"> '. $produit->getPrixUnit() .'€ </p>
                                        <a href="/PageProduits/'. urlencode($produit->getNom()) .'" class="btn btn-outline-dark mx-4" name="Produit" value="'. $produit->getNom() .'">
                                        Voir le produit <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                        <path fill-rule="evenodd" d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8"/>
                                      </svg></a>
                                    </div>
                                </div>';
                                }
                                
                                else{
                                    $html .= '                <div class="card shadow-card me-4 mt-5" style="width: 18rem;">'. $addhtml .'
                                    <img src="/public'. $produit->getImageUrl() .'" class="card-img-top" alt="...">
                                    <div class="card-body">
                                        <h5 class="card-title">'. $produit->getNom() .'</h5>
                                        <p class="card-text text-center fs-4 fw-lighter">- '. $produit->getPrixUnit() .'€ -</p>
                                        <a href="/PageProduits/'. urlencode($produit->getNom()) .'" class="btn btn-outline-dark mx-4" name="Produit" value="'. $produit->getNom() .'">
                                        Voir le produit <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                        <path fill-rule="evenodd" d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8"/>
                                      </svg></a>
                                    </div>
                                </div>';
                                }
                            }


                            echo $html;

                            if($html == ''){
                                echo '<div class="container mt-5 mb-5">';
                                echo '<h1 class="mt-5 mb-5 text-center">Aucun produits correspond à cette recherche...</h1>';
                                echo '</div>';
                            }
                            else{
                                echo '<h1 class="mt-5 text-center">Aucun autres produits correspond à cette recherche...</h1>';
                            }
                        
                        ?>
                        
                    </div>

                    
                </div>
            </form>
        </div>

        <!-- Footer -->
        <?php include_once ROOT.'/view/template/footer.php'; ?>

        <!-- Modal -->
        <?php include_once ROOT.'/view/template/panier.php'; ?>

        <!-- Chargement javascript -->
        <!-- js pour bootstrap -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script>
        <script src="<?php ROOT?>/Js/ModifQtePanier.js"></script>

    </body>
</html>
