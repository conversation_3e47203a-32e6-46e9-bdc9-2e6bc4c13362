<?php
class LivraisonController extends Controller {
    public static function index($params) {
        
        // Vérifie si l'utilisateur est connecté
        if (!isset($_SESSION['user'])) {
            header("Location: /Connexion/");
            exit();
        }

        // Si le formulaire est soumis
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validation des champs
            if (!empty($_POST['nom']) && !empty($_POST['prenom']) && !empty($_POST['adresse']) && 
                !empty($_POST['cp']) && !empty($_POST['ville']) && !empty($_POST['pays'])) {

                // Récupère l'utilisateur actuel
                $username = $_SESSION['user'];
                
                // Met à jour les informations de livraison dans la base de données
                DBManagerClient::ModifNameClient(htmlspecialchars($_POST['nom']), $username);
                DBManagerClient::ModifFirstNameClient(htmlspecialchars($_POST['prenom']), $username);
                DBManagerClient::ModifAPClient(htmlspecialchars($_POST['adresse']), $username);
                DBManagerClient::ModifCPClient(htmlspecialchars($_POST['cp']), $username);
                DBManagerClient::ModifVilleClient(htmlspecialchars($_POST['ville']), $username);
                DBManagerClient::ModifPaysClient(htmlspecialchars($_POST['pays']), $username);
                
                header("Location: /Paiement/");
                exit();

            }
        }

        // Pré-remplir les champs avec les données du client
        $client = DBManagerClient::getClientByName2($_SESSION['user']);
        $params['nom'] = $client->getNom();
        $params['prenom'] = $client->getPrenom();
        $params['adresse'] = $client->getAdresse_postale();
        $params['cp'] = $client->getCode_postal();
        $params['ville'] = $client->getVille();
        $params['pays'] = $client->getPays();

        // Affiche la vue
        $view = ROOT."/view/pagelivraison.php";
        self::render($view, $params);
    }
}
?>
