/* Dans la modification du mot de passe, on met une alerte si le mot de passe saisie n'est pas identique*/
function submit_form(){
    var mdp_modif = document.getElementById('mdp2').value;
    var mdp_conf = document.getElementById('mdpconfirmation').value;

    if(mdp_modif != mdp_conf){
        alert("Les mots de passes doit être identique !");
    }
    else{
        document.forms['form_mdp'].submit()
    }
}

// Fonction pour basculer entre le mode "text" et "password"
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const icon = document.getElementById(inputId + '-icon');

    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}
