<!DOCTYPE html>
<html lang="fr-FR">
    <head>
        <title>CSGOSHOP</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <!-- inclus bootstrap via CDN = Content Delivery Network (réseau de distribution de contenu) -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <!-- inclus style.css -->
        <link href="<?php ROOT?>/view/CSS/style5.css" rel="stylesheet">

    </head>
    <body>
        <div class="login-container">
            <!-- Logo CSGOSHOP -->
            <div class="logo">
                <img src="<?php ROOT?>/public/Images/CSGOSHOP LOGO.png" alt="CSGOSHOP Logo">
            </div>

            <!-- Titre du formulaire -->
            <h1>Connexion BackOffice</h1>

            <!-- Erreur de connexion -->
            <?php if (isset($params['error'])){
                echo '<div class="alert alert-danger">'.$params['error'].'</div>';
            }
            ?>

            <!-- Formulaire de connexion -->
            <form action="/BO-Admin/" method="POST">
                <!-- Champ Nom d'utilisateur -->
                <div class="mb-3">
                    <label for="username" class="form-label">Nom d'utilisateur</label>
                    <input type="text" class="form-control" id="username" name="username" placeholder="Utilisateur" required>
                </div>

                <!-- Champ Mot de passe -->
                <div class="mb-3">
                    <label for="password" class="form-label">Mot de passe</label>
                    <input type="password" class="form-control" id="password" name="password" placeholder="Mot de passe" required>
                </div>

                <!-- Bouton de connexion -->
                <button type="submit" class="mt-3 btn btn-login">Se connecter</button>
            </form>
        </div>
    </body>
</html>
