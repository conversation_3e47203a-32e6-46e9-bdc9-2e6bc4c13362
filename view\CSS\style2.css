body {
    background: linear-gradient(65deg, #383F51, #1d0feb);
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

/* Style du conteneur principal */
.container {
    margin-top: 2rem;
}

/* Style du logo */
.col img {
    transition: transform 0.3s ease;
}

.col img:hover {
    transform: scale(1.05);
}

/* Style du formulaire */
.shadow-sm {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
    border: none !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.shadow-sm:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
}

/* Style des inputs */
.form-control {
    border: 2px solid #e0e0e0;
    padding: 0.8rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

.form-control:focus {
    border-color: #1d0feb;
    box-shadow: 0 0 0 0.2rem rgba(29, 15, 235, 0.15);
}

/* Style du bouton de connexion */
.btn-outline-dark {
    border: 2px solid #383F51;
    padding: 0.5rem 2rem;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-outline-dark:hover {
    background: linear-gradient(65deg, #383F51, #1d0feb);
    border-color: transparent;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(29, 15, 235, 0.3);
}

/* Style des liens de retour et d'inscription */
.underline-change,
.underline-change2 {
    text-decoration: none;
    position: relative;
    color: #fff !important;
    font-weight: 500;
    transition: all 0.3s ease;
}

.underline-change::after {
    content: '';
    position: absolute;
    width: 80px;
    height: 2px;
    background: #fff;
    bottom: -5px;
    left: 7.5%;
    border-radius: 40px;
    transform-origin: right;
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.underline-change2::after {
    content: '';
    position: absolute;
    width: 230px;
    height: 2px;
    background: #fff;
    bottom: -5px;
    left: 41.3%;
    border-radius: 40px;
    transform-origin: right;
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.underline-change:hover::after,
.underline-change2:hover::after {
    transform-origin: left;
    transform: scaleX(1);
}

/* Style du titre */
h1 {
    color: #383F51;
    font-weight: 600;
    margin-bottom: 2rem;
}

/* Animation d'entrée du formulaire */
.shadow-sm {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
