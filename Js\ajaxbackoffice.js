let SelectAdmin = document.getElementById('AdminSelect');
SelectAdmin.addEventListener('change', function() {
    var selectedAdmin = this.value;
    
    fetch("/BO-Admin/Accueil/gestioncompte/ajaxPrivileges/", {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams({ adminName: selectedAdmin })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error("Erreur lors de la requête réseau.");
        }
        return response.text();
    })
    .then(data => {
        
        // Décoche toutes les cases
        document.getElementById("readCheck").checked = false;
        document.getElementById("updateCheck").checked = false;
        document.getElementById("createCheck").checked = false;
        document.getElementById("deleteCheck").checked = false;

        //Active les checkboxes
        document.getElementById("readCheck").disabled = false;
        document.getElementById("updateCheck").disabled = false;
        document.getElementById("createCheck").disabled = false;
        document.getElementById("deleteCheck").disabled = false;
        
        // Coche les cases en fonction des privilèges
        if (data.includes("SELECT")) document.getElementById("readCheck").checked = true;
        if (data.includes("UPDATE")) document.getElementById("updateCheck").checked = true;
        if (data.includes("CREATE")) document.getElementById("createCheck").checked = true;
        if (data.includes("DELETE")) document.getElementById("deleteCheck").checked = true;
        if (data.includes("ALL PRIVILEGES")) {
            document.getElementById("readCheck").checked = true;
            document.getElementById("updateCheck").checked = true;
            document.getElementById("createCheck").checked = true;
            document.getElementById("deleteCheck").checked = true;

            //Désactive les checkboxes
            document.getElementById("readCheck").disabled = true;
            document.getElementById("updateCheck").disabled = true;
            document.getElementById("createCheck").disabled = true;
            document.getElementById("deleteCheck").disabled = true;
        }
    })
    .catch(error => {
        console.error("Erreur:", error);
    });
});