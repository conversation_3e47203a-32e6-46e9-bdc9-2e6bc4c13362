/* Progress Steps */
.progress-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 800px;
    margin: 3rem auto;
    position: relative;
    padding: 0 20px;
}

.progress-steps::before {
    content: '';
    position: absolute;
    top: 35px;
    left: 0;
    width: 100%;
    height: 3px;
    background: #e0e0e0;
    z-index: 1;
}

.progress-steps::after {
    content: '';
    position: absolute;
    top: 35px;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(65deg, #383F51, #1d0feb);
    z-index: 1;
    transition: width 0.3s ease;
}

.step {
    position: relative;
    z-index: 2;
    width: 70px;
    text-align: center;
}

.step-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    border: 3px solid #e0e0e0;
    transition: all 0.3s ease;
    font-size: 1.5rem;
}

.step.active .step-icon {
    border-color: #1d0feb;
    background: linear-gradient(65deg, #383F51, #1d0feb);
    color: white;
    box-shadow: 0 0 20px rgba(29, 15, 235, 0.3);
}

.step-label {
    margin-top: 1rem;
    font-weight: 600;
    color: #383F51;
    font-size: 0.9rem;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.step.active .step-label {
    opacity: 1;
    color: #1d0feb;
}

/* Formulaire */
.form-control {
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 0.8rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #1d0feb;
    box-shadow: 0 0 0 0.2rem rgba(29, 15, 235, 0.15);
}

.form-label {
    font-weight: 500;
    color: #383F51;
    margin-bottom: 0.5rem;
}

/* Select personnalisé */
select.form-control {
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%23383F51' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14L2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    padding-right: 2.5rem;
}

/* Conteneurs des champs de paiement */
#card_fields, #paypal_fields {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    margin-top: 1rem;
    border: 1px solid #e0e0e0;
}

/* Boutons */
.btn {
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline-dark {
    border: 2px solid #383F51;
    color: #383F51;
    background: transparent;
}

.btn-outline-dark:hover {
    background: #383F51;
    color: white;
}

.btn-primary {
    background: linear-gradient(65deg, #383F51, #1d0feb);
    border: none;
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(29, 15, 235, 0.3);
}

/* Titre */
h2 {
    color: #383F51;
    font-weight: 600;
    margin-bottom: 2rem;
    text-align: center;
}

/* Responsive */
@media (max-width: 768px) {
    .progress-steps {
        padding: 0 10px;
    }

    .step-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .step-label {
        font-size: 0.8rem;
    }
}

/* Footer */

html, body {
    height: 100%;
    margin: 0;
    padding: 0;
}

.page-wrapper {
    min-height: 100%;
    position: relative;
    padding-bottom: 200px; 
}

.content {
    width: 100%;
}

footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #000000;
    z-index: 1000;
}

/* Ajustements pour le wrapper des icônes sociales */
.wrapper {
    padding-top: 20px;
    padding-bottom: 20px;
    margin-bottom: 0;
    height: auto;
}


