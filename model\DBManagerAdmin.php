<?php

class DBManagerAdmin {
    private static ?\PDO $cnx;
    private static Admin $admin;
    private static array $LesAdmins;

    public function __construct(){
        self::$admin = new Admin("", "", "", "", "", "", "");
    }

    /**
     * Permet de récupérer l'id' d'un Admin dans une base de donnée. Et retourne si ça existe ou non
     * 
     */
    public static function getAdminByName($ID) {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "SELECT Utilisateur FROM admin Where Utilisateur = ?";

        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);

        // Exécuter la requête
        $sql1->execute([$ID]);

        $resultId = $sql1->fetchColumn();

        return $resultId;
    }

    /**
     * Permet de récupérer le mot de passe d'un admin dans une base de donnée. Et retourne si ça existe ou non
     * 
     */
    public static function getAdmin_Pwd($ID, $Pwd)
    {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "SELECT Password FROM admin Where Password = ? AND Utilisateur = ?";
        
        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);

        // Exécuter la requête
        $sql1->execute([$Pwd, $ID]);

        $resultpass = $sql1->fetchColumn();

        return $resultpass;

    }


    /**
     * Permet de récupérer un Admin dans une base de donnée
     * 
     */
    public static function getAdminByID($ID) {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "SELECT idAdmin, Nom, Prenom, Password, mobile, email, Utilisateur ";
        $query .= "FROM admin Where idAdmin = ?";

        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);

        // Exécuter la requête
        $sql1->execute([$ID]);

        // Récupère la valeur du Admin
        $row = $sql1->fetch();

        if ($row) {
            self::$admin = new Admin($row['idAdmin'], $row['Utilisateur'], $row['Nom'], $row['Prenom'], $row['Password'], $row['mobile'], $row['email']);
        }

        return self::$admin;
    }

    /**
     * Permet de récupérer tous les Admin de la base de données.
     * 
     */
    public static function getAdmins() {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();
        
        //Initialise le tableau des Admin
        self::$LesAdmins = [];

        // Requête SQL
        $query = "SELECT idAdmin, Nom, Prenom, Password, mobile, email, Utilisateur ";
        $query .= "FROM admin";

        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);

        // Exécuter la requête
        $sql1->execute();

        while($row = $sql1->fetch(PDO::FETCH_ASSOC)) {
            $admins = new Admin($row['idAdmin'], $row['Utilisateur'], $row['Nom'], $row['Prenom'], $row['Password'], $row['mobile'], $row['email']);
            self::$LesAdmins[] = $admins;
        }

        return self::$LesAdmins;
    }

    /**
     * Permet de récupérer un Admin de par son username dans une base de donnée. Et retourne le Admin
     * 
     */
    public static function getAdminByName2($ID) {
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();
        
        // Requête SQL
        $query = "SELECT idAdmin, Nom, Prenom, Password, mobile, email, Utilisateur ";
        $query .= "FROM admin Where Utilisateur = ?";

        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);

        // Exécuter la requête
        $sql1->execute([$ID]);

        // Récupère la valeur du Admin
        $row = $sql1->fetch();

        if ($row) {
            self::$admin = new Admin($row['idAdmin'], $row['Utilisateur'], $row['Nom'], $row['Prenom'], $row['Password'], $row['mobile'], $row['email']);
        }

        return self::$admin;
    }


    /**
     * Modifie le nom d'utilisateur de l'utilisateur
     * return bool
     */
    public static function ModifUserNameAdmin($user, $user2){
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "UPDATE admin SET Utilisateur = ? where Utilisateur = ?";
        
        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);
        
        // Exécuter la requête
        $nameuser = $sql1->execute([$user, $user2]);

        return $nameuser;
    }

    /**
     * Modifie le prénom de l'utilisateur
     * return bool
     */
    public static function ModifFirstNameAdmin($prenom ,$user2){
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "UPDATE admin SET Prénom = ? where Utilisateur = ?";
        
        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);
        
        // Exécuter la requête
        $firstname = $sql1->execute([$prenom, $user2]);

        return $firstname;
    }
    /**
     * Modifie le nom de l'utilisateur
     * return bool
     */
    public static function ModifNameAdmin($nom ,$user2){
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "UPDATE admin SET Nom = ? where Utilisateur = ?";
        
        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);
        
        // Exécuter la requête
        $name = $sql1->execute([$nom, $user2]);

        return $name;
    }

    /**
     * Modifie le mot de passe de l'utilisateur
     * return bool
     */
    public static function ModifPasswordAdmin($mdp ,$user2){
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "UPDATE admin SET Password = ? where Utilisateur = ?";
        
        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);
        
        // Exécuter la requête
        $pass = $sql1->execute([$mdp, $user2]);

        return $pass;
    }

    /**
     * Modifie l'email de l'utilisateur
     * return bool
     */
    public static function ModifEmailAdmin($email, $user2){
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "UPDATE admin SET email = ? where Utilisateur = ?";
        
        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);
        
        // Exécuter la requête
        $email = $sql1->execute([$email, $user2]);

        return $email;
    }
    
    /**
     * Modifie le mobile de l'utilisateur
     * return bool
     */
    public static function ModifMobileAdmin($mobile ,$user2){
        //connexion à la BDD
        self::$cnx = DbManager::getConnection();

        // Requête SQL
        $query = "UPDATE admin SET mobile = ? where Utilisateur = ?";
        
        // Préparer la requête
        $sql1 = self::$cnx->prepare($query);
        
        // Exécuter la requête
        $mobile = $sql1->execute([$mobile, $user2]);

        return $mobile;
    }

}
?>