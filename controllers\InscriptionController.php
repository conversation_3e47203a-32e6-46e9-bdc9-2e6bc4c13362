<?php
class InscriptionController extends Controller{


    public static function index($params){

        $params['html'] = '';
        $params['htmlp'] = '';
        $params['htmln'] = '';
        $params['htmlpass'] = '';
        $params['htmlpassconfirm'] = '';
        $params['htmlemail'] = '';
        $params['htmltel'] = '';
        
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Vérification du nom d'utilisateur
            if (!empty($_POST['id'])) {
                $Id = htmlspecialchars($_POST['id']);
            
                $resultId = DBManagerClient::getClientByName($Id);
                
                if ($resultId != false) {
                    $params['html'] .= '<p style="color:red"> Choisissez un autre nom d\'utilisateur * </p>'; // Le nom d'utilisateur est déjà pris
                } elseif (empty(trim($Id))) {
                    $params['html'] .= '<p style="color:red"> Veuillez saisir un nom d\'utilisateur * </p>'; // Le nom d'utilisateur est vide
                }
            } else {
                $params['html'] .= '<p style="color:red"> Veuillez saisir un nom d\'utilisateur * </p>'; // Le champ nom d'utilisateur n'a pas été soumis
            }
            
            // Vérification du prénom
            if (!empty(htmlspecialchars($_POST['prenom']))) {
                $prenom = htmlspecialchars($_POST['prenom']);
                if (empty(trim($prenom))) {
                    $params['htmlp'] .= '<p style="color:red"> Veuillez saisir un prénom * </p>'; // Le prénom est vide
                }
            } else {
                $params['htmlp'] .= '<p style="color:red"> Veuillez saisir un prénom * </p>'; // Le champ prénom n'est pas soumis
            }
            
            // Vérification du nom
            if (!empty(htmlspecialchars($_POST['nom']))) {
                $nom = htmlspecialchars($_POST['nom']);
                if (empty(trim($nom))) {
                    $params['htmln'] .= '<p style="color:red"> Veuillez saisir un nom * </p>'; // Le nom est vide
                }
            } else {
                $params['htmln'] .= '<p style="color:red"> Veuillez saisir un nom * </p>'; // Le champ nom n'a pas été soumis
            }
            
            // Vérification du mot de passe
            if (!empty(htmlspecialchars($_POST['password']))) {
                $pass1 = htmlspecialchars($_POST['password']);
                $countpass = strlen($pass1);
                $listedecaractère = array( 1 => '@', 2 => '$', 3 => '?', 4 => '!', 5 => '*');
                function verifymdp($listedecaractère, $pass1){
                    $nombredec = 0;
                    foreach($listedecaractère as $uncaractere){
                        $verifie = str_contains($pass1, $uncaractere);
                        if($verifie){
                            $nombredec += 1;
                        } else{
                            $nombredec += 0;
                        }
                    }
                    return $nombredec;
                }
        
                if (empty(trim($pass1))) {
                    $params['htmlpass'] .= '<p style="color:red"> Veuillez saisir un mot de passe valide * </p>'; // Le mot de passe est vide
                } elseif ($pass1 !== htmlspecialchars($_POST['confirmpassword'])) {
                    $params['htmlpassconfirm'] .= '<p style="color:red"> Les mots de passe ne se correspondent pas * </p>'; // Les mots de passe ne correspondent pas
                } elseif ($countpass < 9){
                    $params['htmlpassconfirm'] .= '<p style="color:red"> Le mot de passe doit faire minimum 8 caractères. * </p>'; // Le mots de passe ne fait pas 8 caractères.
                } elseif(verifymdp($listedecaractère, $pass1) == 0){
                    $params['htmlpassconfirm'] .= '<p style="color:red"> Le mot de passe ne possède pas de caractères spéciaux * </p>'; // Le mots de passe n'a pas de caractères spéciaux.
                }
            } else {
                $params['htmlpass'] .= '<p style="color:red"> Veuillez saisir un mot de passe valide * </p>'; // Le champ mot de passe n'a pas été soumis
            }
            
            // Vérification de l'email
            if (!empty(htmlspecialchars($_POST['email']))) {
                $mail = htmlspecialchars($_POST['email']);
        
                $tabemail = ['@gmail.com', '@outlook.fr', '@hotmail.fr', '@yahoo.fr'];
                function verifieemail($content, $tabemail){
                    $nombremailvalide = 0;
                    for($i = 0; $i < count($tabemail); $i++){
                        $vérif = str_contains($content, $tabemail[$i]);
                        if($vérif == true){
                            $nombremailvalide += 1;
                        }
                    }
                    return $nombremailvalide;
                }
        
                if (empty(trim($mail))) {
                    $params['htmlemail'] .= '<p style="color:red"> Veuillez saisir une adresse email * </p>'; // L'email est vide
                } else if(verifieemail($mail, $tabemail) == 0){
                    $params['htmlemail'] .= '<p style="color:red"> Veuillez saisir une adresse email valide * </p>'; // L'email est pas valide
                }
            } else {
                $params['htmlemail'] .= '<p style="color:red"> Veuillez saisir une adresse email * </p>'; // Le champ email n'a pas été soumis
            }
            
            // Vérification du numéro de téléphone
            if (!empty(htmlspecialchars($_POST['tel']))) {
                $tel = htmlspecialchars($_POST['tel']);
                if (strlen($tel) !== 10) {
                    $params['htmltel'] .= '<p style="color:red"> Veuillez saisir un numéro de téléphone valide commençant par +33 * </p>'; // Le numéro de téléphone n'est pas valide
                }
            } else {
                $params['htmltel'] .= '<p style="color:red"> Veuillez saisir un numéro de téléphone * </p>'; // Le champ téléphone n'a pas été soumis
            }
            
            // Si aucune erreur n'est détectée, procéder à l'insertion
            if ($params['html'] === '' && $params['htmlp'] === '' && $params['htmln'] === '' && $params['htmlpass'] === '' && $params['htmlpassconfirm'] === '' && $params['htmlemail'] === '' && $params['htmltel'] === '') {
                DBManagerClient::InsertClient($nom, $prenom, $pass1, $tel, $mail, $Id);
                header("Location: /ValidationInscription/"); // Redirection vers la page de validation de l'inscription
                exit();
            }
        }

        // appelle la vue
        $view = ROOT."/view/Auth/inscription.php";
        self::render($view, $params);
    }

}
?>