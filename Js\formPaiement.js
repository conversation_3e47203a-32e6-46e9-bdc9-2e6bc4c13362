document.addEventListener('DOMContentLoaded', function() {
    const paymentMethod = document.getElementById('payment_method');
    const cardFields = document.getElementById('card_fields');
    const paypalFields = document.getElementById('paypal_fields');
    const submitButton = document.getElementById('button_paiement');

    // Gestion du changement de méthode de paiement
    paymentMethod.addEventListener('change', function() {
        // Cache tous les champs
        cardFields.style.display = 'none';
        paypalFields.style.display = 'none';

        // Affiche les champs appropriés
        if (this.value === 'card') {
            cardFields.style.display = 'block';
        } else if (this.value === 'paypal') {
            paypalFields.style.display = 'block';
        }
    });

    // Validation du formulaire
    submitButton.addEventListener('click', function(e) {
        e.preventDefault();
        const method = paymentMethod.value;

        if (!method) {
            alert('Veuillez choisir une méthode de paiement');
            return;
        }

        if (method === 'card') {
            const cardNumber = document.getElementById('card_number').value;
            const expiryDate = document.getElementById('expiry_date').value;
            const cvv = document.getElementById('cvv').value;
            const cardName = document.getElementById('card_name').value;

            if (!cardNumber || !expiryDate || !cvv || !cardName) {
                alert('Veuillez remplir tous les champs de la carte bancaire');
                return;
            }
        } 
        else if (method === 'paypal') {
            const paypalEmail = document.getElementById('paypal_email').value;
            
            if (!paypalEmail) {
                alert('Veuillez entrer votre email PayPal');
                return;
            }
        }

        // Si tout est rempli, soumet le formulaire
        document.getElementById('form_paiement').submit();
    });
});
